<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>学习总结 (20250725)</title>
    <style>
        body { font-family: sans-serif; line-height: 1.6; }
        h1, h2, h3 { color: #333; }
        .container { max-width: 800px; margin: auto; padding: 20px; }
        .summary, .details, .learning, .reflection, .plan, .appendix { margin-bottom: 30px; }
        h2, h3 { scroll-margin-top: 20px; }
        .toc ul { list-style-type: none; padding-left: 0; }
        .toc > ul > li { margin-bottom: 10px; }
        .toc ul ul { padding-left: 20px; margin-top: 5px; }
        .toc ul ul li { margin-bottom: 5px; }
        .toc a { text-decoration: none; color: #0066cc; }
        .toc a:hover { text-decoration: underline; }
        .github-link {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .github-link a {
            color: #0366d6;
            text-decoration: none;
            font-family: monospace;
            font-size: 14px;
        }
        .github-link a:hover { text-decoration: underline; }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin-top: 20px;
        }
        .note p { margin: 0; }
        code {
            background-color: #f1f3f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .code-block pre {
            margin: 0;
            padding: 20px;
            background: none;
            border: none;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            color: #333;
        }
        .code-block code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: inherit;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>学习总结</h1>
        <p><strong>姓名：</strong> 常一诺</p>
        <p><strong>日期：</strong> 2025年7月25日</p>

        <div class="toc">
            <h2>目录</h2>
            <ul>
                <li><a href="#details">一、书籍PDF电子文本化工作进展</a>
                    <ul>
                        <li><a href="#tech-research">1.1 技术调研</a></li>
                        <li><a href="#scan-pdf">1.2 扫描版PDF提取脚本编写</a></li>
                        <li><a href="#native-pdf">1.3 原生PDF提取脚本编写</a></li>
                        <li><a href="#testing">1.4 效果测试与评估</a></li>
                    </ul>
                </li>
                <li><a href="#learning">二、机器学习学习进展</a>
                    <ul>
                        <li><a href="#glm">2.1 广义线性模型</a></li>
                        <li><a href="#classification">2.2 二分类与多分类任务</a></li>
                        <li><a href="#decision-tree">2.3 决策树</a></li>
                        <li><a href="#ensemble">2.4 集成学习方法</a></li>
                    </ul>
                </li>
                <li><a href="#appendix">附录：相关资源与源代码</a></li>
            </ul>
        </div>

        <div class="details">
            <h2 id="details">一、 书籍PDF电子文本化工作进展</h2>
            <h3 id="tech-research">1.1 技术调研</h3>
            <p>
                针对扫描版PDF文本提取需求，系统调研了多种OCR（光学字符识别）技术方案。
                <ul>
                    <li><strong>方案1（Tesseract OCR）：</strong>它的优点是技术成熟，但缺点是对于复杂的PDF（如文本方向不定、含有大量表格）识别效果不佳。</li>
                    <li><strong>方案2（Miner-U OCR）：</strong>它的优点是能够应对复杂场景，但缺点是目前网络上流传的关于它的识别效果多数是一些零散的用户的反馈，缺乏官方的、正规的测试，且运行该模型非常耗资源。</li>
                    <li><strong>方案3（Paddle OCR）：</strong>它的优点是既能应对复杂场景，又有官方的测试数据支撑，可信度更高，且比起Miner-U识别速度更快。</li>
                </ul>
                综合考虑技术成熟度、处理效果和计算效率，最终选择采用PaddleOCR方案。
            </p>

            <h3 id="scan-pdf">1.2 扫描版PDF提取脚本编写</h3>
            <p>
                基于PaddleOCR框架开发扫描版PDF处理脚本，采用PP-StructureV3模型作为核心引擎。该模型具备文档结构化识别能力，支持表格、图表等复杂元素的提取，并集成了文档方向自动识别功能，对中英文混合文档具有良好的适应性。
                <br>
                <strong>遇到的挑战：</strong>
                <ul>
                    <li>测试使用的《桑福德抗微生物治疗指南》扫描质量不高，且其内容主要是各种复杂的表格，识别出的内容可能与原文档有比较大的出入；</li>
                    <li>测试使用的《哈里森感染病学》这本书的PDF很奇怪，它的每一页都异乎寻常的小，导致识别出来的内容都是图片；</li>
                    <li>由于我的计算机不支持使用GPU来运行Paddle OCR，只能使用CPU进行识别，导致识别的速度非常慢，即使是测试每个提供的扫描版PDF文档的21页内容，整个程序运行下来也需要5、6小时。</li>
                </ul>
                <strong>解决方案：</strong>
                <ul>
                    <li>对于《桑福德》这种情况可以通过手动对图片进行预处理、调整模型的参数来提高识别质量;</li>
                    <li>对于《哈里森》这种情况可能需要寻找新的PDF;</li>
                    <li>等工作电脑分派下来后使用GPU运行应该能快不少。</li>
                </ul>                
            </p>

            <h3 id="native-pdf">1.3 原生PDF提取脚本编写</h3>
            <p>
                针对原生PDF文档，采用PyMuPDF库进行文本流的直接解析和提取。
                <br>
                <strong>实现的功能：</strong>
                <ul>
                    <li>保留了基本的段落格式。</li>
                    <li>尝试提取了表格和列表。</li>
                </ul>
                <strong>遇到的问题：</strong>对于表格的识别情况很糟糕，因为原生PDF在制作时很可能没有使用标准的表格来绘制表格，而是使用一些竖线等符号来划分不同单元格的内容，导致提取不出表格的结构，而只能提取出一些无意义的符号。
                <br>
                <strong>解决方案：</strong>拟通过OCR方式来处理原生PDF，但还需要测试识别效果。
            </p>

            <h3 id="testing">1.4 效果测试与评估</h3>
            <p>
                基于提供的10本医学专业书籍PDF，每本选取21页样本进行系统性测试评估。
                <br>
                <strong>测试结果：</strong>
                <ul>
                    <li>扫描版PDF平均置信度约为96%，对于纯文本的识别效果非常好，准确率能够达到99%，对于多表格的识别效果尚可，准确率也能达到85%~95%，如果是比较标准、结构简单的表格，识别效果也很好，但如果是无框线或结构复杂的表格识别效果就没有那么好。</li>
                    <li>原生PDF的文本提取完整度达到80%左右，且只能提取出文本内容，而无法保留原有的表格等结构。</li>
                </ul>
                测试结果表明，复杂表格识别和原生PDF结构化提取仍存在优化空间，需要进一步改进算法和参数配置。
            </p>
        </div>

        <div class="learning">
            <h2 id="learning">二、 机器学习学习进展</h2>
            <h3 id="glm">2.1 广义线性模型 (Generalized Linear Models)</h3>
            <p>
                广义线性模型是传统线性回归的扩展，能够处理非正态分布的响应变量。它由三个核心组成部分构成：
                <ul>
                    <li><strong>随机成分：</strong>响应变量Y服从指数族分布（如正态分布、二项分布、泊松分布等）</li>
                    <li><strong>系统成分：</strong>线性预测器η = β₀ + β₁x₁ + β₂x₂ + ... + βₚxₚ</li>
                    <li><strong>链接函数：</strong>连接期望值E(Y)和线性预测器η的函数，如logit链接、log链接等</li>
                </ul>
                <strong>常见应用：</strong>
                <ul>
                    <li>逻辑回归（二项分布 + logit链接）用于分类问题</li>
                    <li>泊松回归（泊松分布 + log链接）用于计数数据</li>
                    <li>线性回归是GLM的特例（正态分布 + 恒等链接）</li>
                </ul>
            </p>

            <h3 id="classification">2.2 二分类与多分类任务</h3>
            <p>
                分类是监督学习的重要任务，根据类别数量可分为：
                <ul>
                    <li><strong>二分类任务：</strong>预测两个类别中的一个，输出通常为0/1或正/负类
                        <ul>
                            <li>常用算法：逻辑回归、SVM、朴素贝叶斯、决策树等</li>
                            <li>评估指标：准确率、精确率、召回率、F1-score、AUC-ROC等</li>
                            <li>应用场景：垃圾邮件检测、医疗诊断、信用评估等</li>
                        </ul>
                    </li>
                    <li><strong>多分类任务：</strong>预测多个类别（≥3个）中的一个
                        <ul>
                            <li>策略：One-vs-Rest（一对多）、One-vs-One（一对一）</li>
                            <li>原生支持：决策树、随机森林、神经网络、k-NN等</li>
                            <li>评估指标：多类准确率、宏平均/微平均F1、混淆矩阵等</li>
                            <li>应用场景：图像分类、文本分类、手写数字识别等</li>
                        </ul>
                    </li>
                </ul>
                <strong>关键概念：</strong>特征工程、交叉验证、过拟合与欠拟合、偏差-方差权衡等对分类性能有重要影响。
            </p>

            <h3 id="decision-tree">2.3 决策树</h3>
            <p>
                决策树是一种基于树结构的非线性模型，具有很强的可解释性。核心学习内容包括：
                <ul>
                    <li><strong>基本思想：</strong> 通过一系列的“是/否”问题来进行决策和分类，最终落到一个叶子节点得到预测结果。</li>
                    <li><strong>特征选择标准：</strong>
                        <ul>
                            <li><strong>信息增益：</strong>基于熵的减少量，ID3算法使用</li>
                            <li><strong>信息增益率：</strong>对信息增益的改进，C4.5算法使用</li>
                            <li><strong>基尼不纯度：</strong>衡量数据集的混乱程度，CART算法使用</li>
                        </ul>
                    </li>
                    <li><strong>优缺点：</strong>
                        <ul>
                            <li>优点：易理解、无需数据预处理、能处理数值和类别特征</li>
                            <li>缺点：容易过拟合、对噪声敏感、可能产生偏向某些特征的偏差</li>
                        </ul>
                    </li>
                    <li><strong>剪枝技术：</strong>预剪枝（提前停止）和后剪枝（构建后删除）来防止过拟合</li>
                </ul>
            </p>

            <h3 id="ensemble">2.4 集成学习方法</h3>
            <p>
                集成学习通过组合多个学习器来提升整体性能，是机器学习中的重要技术：
                <ul>
                    <li><strong>Bagging（装袋法）：</strong>
                        <ul>
                            <li>原理：并行训练多个模型，通过投票或平均来决策</li>
                            <li>代表算法：随机森林（Random Forest）</li>
                            <li>优点：减少过拟合，提高泛化能力</li>
                        </ul>
                    </li>
                    <li><strong>Boosting（提升法）：</strong>
                        <ul>
                            <li>原理：串行训练，后续模型重点关注前面模型的错误</li>
                            <li>代表算法：AdaBoost、梯度提升树（GBDT）、XGBoost</li>
                            <li>优点：能显著提升弱学习器的性能</li>
                        </ul>
                    </li>
                    <li><strong>Stacking（堆叠法）：</strong>使用元学习器来组合基学习器的预测结果</li>
                </ul>
                <strong>应用优势：</strong>集成方法通常比单一模型具有更好的预测性能和鲁棒性。
            </p>
        </div>

        <div class="appendix">
            <h2 id="appendix">附录：相关资源与源代码</h2>

            <h3>处理结果GitHub仓库</h3>
            <p>
                本周完成的书籍PDF电子文本化工作的处理结果已上传至GitHub仓库：
            </p>
            <div class="github-link">
                <p><strong>GitHub仓库地址：</strong></p>
                <p><a href="https://github.com/OLIVIA-ATIS/extract" target="_blank" rel="noopener noreferrer">
                    https://github.com/OLIVIA-ATIS/extract
                </a></p>
            </div>

            <h3>GitHub仓库结构</h3>
            <ul>
                <li><strong>input_scanned/</strong> - 扫描版PDF测试文件</li>
                <li><strong>output_scanned/</strong> - 扫描版PDF处理结果</li>
                <li><strong>input_native/</strong> - 原生PDF测试文件</li>
                <li><strong>output_native/</strong> - 原生PDF处理结果</li>
                <li><strong>README.md</strong> - 项目说明文档</li>
            </ul>

            <h3>源代码</h3>
            <p>以下是当前版本的完整源代码：</p>

            <h4>扫描版PDF处理脚本（processor.py）</h4>
            <div class="code-block">
                <pre><code>import os
import json
from pathlib import Path
from paddleocr import PPStructureV3

def process(input_dir="input", output_dir="output"):
    """
    使用 predict_iter 批量处理PDF文件

    Args:
        input_dir (str): 存放PDF文件的输入文件夹路径
        output_dir (str): 用于保存处理结果的输出文件夹路径
    """
    # --- 1. 初始化 PPStructureV3 模型 ---
    # 开启文档方向识别和表格识别
    print("正在初始化 PP-StructureV3 模型...")
    pipeline = PPStructureV3(
        use_doc_orientation_classify=True,
        use_table_recognition=True,
        use_doc_unwarping=False
    )
    print("模型初始化完成。")

    # --- 2. 准备输入和输出目录 ---
    if not os.path.exists(input_dir):
        print(f"错误：输入目录 '{input_dir}' 不存在。请创建并放入PDF文件。")
        return

    os.makedirs(output_dir, exist_ok=True)
    print(f"结果将被保存在 '{output_dir}' 目录下。")

    # --- 3. 遍历并处理所有PDF文件 ---
    pdf_files = [f for f in os.listdir(input_dir) if f.lower().endswith('.pdf')]
    if not pdf_files:
        print(f"在 '{input_dir}' 目录中没有找到PDF文件。")
        return

    print(f"找到 {len(pdf_files)} 个PDF文件待处理。")

    for filename in pdf_files:
        pdf_path = os.path.join(input_dir, filename)
        doc_name = Path(pdf_path).stem
        print(f"\n--- 开始处理文档: {filename} ---")

        # --- 4. 为每个PDF创建独立的输出文件夹 ---
        doc_output_dir = os.path.join(output_dir, doc_name)
        os.makedirs(doc_output_dir, exist_ok=True)

        try:
            # --- 5. 使用 predict_iter() 获取一个迭代器 ---
            results_iterator = pipeline.predict_iter(input=pdf_path)

            all_pages_json_data = []
            all_pages_markdown_info = []
            page_count = 0

            print("开始逐页分析和聚合...")
            # --- 6. 通过 for 循环驱动实际的逐页处理 ---
            # 每次循环，才会真正处理一页，从而保持较低且稳定的内存占用
            for page_result in results_iterator:
                page_count += 1
                print(f"  正在处理第 {page_count} 页...")

                # 从每页的结果对象中提取所需信息
                all_pages_json_data.append(page_result.json)
                all_pages_markdown_info.append(page_result.markdown)

            if page_count == 0:
                print(f"警告：文档 '{filename}' 可能为空或未能处理任何页面。")
                continue

            print(f"文档共有 {page_count} 页。正在整合并保存最终文件...")

            # --- 7. 保存整合后的完整结果 ---
            # a) 将所有页面的JSON数据保存为单个文件
            json_save_path = os.path.join(doc_output_dir, f"{doc_name}.json")
            with open(json_save_path, 'w', encoding='utf-8') as f:
                json.dump(all_pages_json_data, f, ensure_ascii=False, indent=4)
            print(f"JSON 结果已保存到: {json_save_path}")

            # b) 使用内置的拼接方法整合所有页面的Markdown并保存
            full_markdown_content = pipeline.concatenate_markdown_pages(all_pages_markdown_info)
            md_save_path = os.path.join(doc_output_dir, f"{doc_name}.md")
            with open(md_save_path, 'w', encoding='utf-8') as f:
                f.write(full_markdown_content)
            print(f"Markdown 结果已保存到: {md_save_path}")

            print(f"--- 文档: {filename} 处理完成 ---")

        except Exception as e:
            # 捕获处理单个文件时可能发生的错误，使脚本能继续处理下一个文件
            print(f"处理文档 {filename} 时发生严重错误: {e}")

    print("\n所有任务已完成。")


if __name__ == "__main__":
    # 调用主函数，开始处理
    process(input_dir="input", output_dir="output")
</code></pre>
            </div>

            <h4>原生PDF处理脚本（native.py）</h4>
            <div class="code-block">
                <pre><code>"""
原生PDF处理脚本
专门处理原生PDF文档，提取文本、表格、图片等结构化信息
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime
import fitz  # PyMuPDF
import pandas as pd
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class NativePDFProcessor:
    """原生PDF处理器"""
    
    def __init__(self, config):
        self.config = config
        
    def extract_text_blocks(self, page):
        """提取文本块，过滤页眉页脚"""
        blocks = page.get_text("dict")["blocks"]

        # 调试模式：显示所有原始文本块
        debug_mode = self.config.get("debug", False)
        if debug_mode:
            logger.info(f"原始文本块数量: {len(blocks)}")
            for i, block in enumerate(blocks):
                if "lines" in block:
                    text_content = ""
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text_content += span["text"]
                    logger.info(f"Block {i}: {text_content[:100]}... (bbox: {block['bbox']})")
        
        # 页面尺寸
        page_height = page.rect.height
        page_width = page.rect.width
        
        # 定义页眉页脚区域（更保守的设置）
        header_threshold = page_height * 0.05  # 上方5%
        footer_threshold = page_height * 0.95  # 下方5%
        
        filtered_blocks = []
        
        for block in blocks:
            if "lines" not in block:
                continue

            bbox = block["bbox"]
            x0, y0, x1, y1 = bbox

            # 根据配置决定是否过滤页眉页脚
            if self.config.get("filter_headers_footers", True) and (y0 < header_threshold or y1 > footer_threshold):
                # 检查是否是页码（通常在页脚中央或角落）
                text_content = ""
                for line in block["lines"]:
                    for span in line["spans"]:
                        text_content += span["text"]
                
                # 检查是否是页码（支持多种格式）
                page_patterns = [
                    r'^\s*\d+\s*$',                    # 纯数字: "5"
                    r'^\s*-\s*\d+\s*-\s*$',           # 带横线: "- 5 -"
                    r'^\s*Page\s+\d+\s*$',            # Page 5
                    r'^\s*\d+\s*/\s*\d+\s*$',         # 5/10
                    r'^\s*\d+\s+of\s+\d+\s*$',        # 5 of 10
                ]

                is_page_number = any(re.match(pattern, text_content.strip(), re.IGNORECASE)
                                   for pattern in page_patterns)
                if is_page_number:
                    continue

                # 检查是否是常见的页眉页脚内容
                header_footer_patterns = [
                    r'^\s*chapter\s+\d+\s*$',         # Chapter 1
                    r'^\s*section\s+\d+\s*$',         # Section 1
                    r'^\s*\d{4}-\d{2}-\d{2}\s*$',     # 日期格式
                    r'^\s*copyright\s+.*$',           # Copyright
                    r'^\s*confidential\s*$',          # Confidential
                    r'^\s*draft\s*$',                 # Draft
                ]

                is_header_footer = any(re.match(pattern, text_content.strip(), re.IGNORECASE)
                                     for pattern in header_footer_patterns)
                if is_header_footer:
                    continue

                # 如果在页眉页脚区域且文本很短，跳过
                if len(text_content.strip()) < 5:  # 降低阈值，避免过度过滤
                    continue
            
            # 过滤极端边缘内容（更宽松的边距检查）
            # 只过滤真正在页面边缘的内容
            extreme_margin = page_width * 0.02  # 只过滤最边缘2%的内容
            if x0 < extreme_margin or x1 > page_width - extreme_margin:
                continue
                
            filtered_blocks.append(block)
            
        return filtered_blocks
    
    def extract_tables(self, page):
        """提取表格"""
        tables = []
        try:
            # 使用PyMuPDF的表格提取功能
            page_tables = page.find_tables()
            
            for i, table in enumerate(page_tables):
                table_data = table.extract()
                if table_data:
                    # 转换为HTML格式
                    df = pd.DataFrame(table_data[1:], columns=table_data[0])
                    html_content = df.to_html(index=False, border=1)
                    
                    tables.append({
                        "table_id": f"table_{i}",
                        "html_content": html_content,
                        "bbox": table.bbox,
                        "row_count": len(table_data),
                        "col_count": len(table_data[0]) if table_data else 0
                    })
        except Exception as e:
            logger.warning(f"表格提取失败: {e}")
            
        return tables
    
    def extract_images(self, page, img_dir, page_num):
        """提取图片"""
        images = []
        try:
            image_list = page.get_images()
            
            for i, img in enumerate(image_list):
                xref = img[0]
                pix = fitz.Pixmap(page.parent, xref)
                
                if pix.n - pix.alpha < 4:  # 确保不是CMYK
                    img_filename = f"page_{page_num}_img_{i}.png"
                    img_path = img_dir / img_filename
                    pix.save(str(img_path))
                    
                    images.append({
                        "image_id": f"img_{i}",
                        "filename": img_filename,
                        "bbox": img[1:5] if len(img) > 4 else None,
                        "size": (pix.width, pix.height)
                    })
                
                pix = None
        except Exception as e:
            logger.warning(f"图片提取失败: {e}")
            
        return images
    
    def process_pdf(self, pdf_path, output_dir):
        """处理PDF文件"""
        pdf_name = Path(pdf_path).stem
        output_dir = Path(output_dir)
        
        # 创建输出目录
        output_dir.mkdir(exist_ok=True, parents=True)
        img_dir = output_dir / "imgs"
        img_dir.mkdir(exist_ok=True)
        
        jsonl_path = output_dir / f"{pdf_name}.jsonl"
        md_path = output_dir / f"{pdf_name}.md"
        
        try:
            with fitz.open(pdf_path) as doc, \
                 open(jsonl_path, 'w', encoding='utf-8') as jsonl_file, \
                 open(md_path, 'w', encoding='utf-8') as md_file:
                
                logger.info(f"处理原生PDF: {pdf_name} ({len(doc)} pages)")
                md_file.write(f"# {pdf_name}\n\n")
                
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    
                    # 提取文本块（过滤页眉页脚）
                    text_blocks = self.extract_text_blocks(page)
                    
                    # 提取表格
                    tables = self.extract_tables(page)
                    
                    # 提取图片
                    images = self.extract_images(page, img_dir, page_num + 1)
                    
                    # 构建内容块
                    content_blocks = []
                    
                    # 处理文本块
                    for i, block in enumerate(text_blocks):
                        text_content = ""
                        for line in block["lines"]:
                            for span in line["spans"]:
                                text_content += span["text"]
                        
                        if text_content.strip():
                            content_blocks.append({
                                "block_id": f"{pdf_name}_p{page_num + 1}_b{i}",
                                "block_type": "text",
                                "content": text_content.strip(),
                                "bbox": block["bbox"],
                                "confidence": None
                            })
                    
                    # 创建页面条目
                    entry = {
                        "document_id": pdf_name,
                        "page_number": page_num + 1,
                        "timestamp": datetime.now().isoformat(),
                        "source_type": "pdf_native",
                        "processing_pipeline": "PyMuPDF",
                        "content_blocks": content_blocks,
                        "tables": tables,
                        "images": images,
                        "metadata": {
                            "total_blocks": len(content_blocks),
                            "total_tables": len(tables),
                            "total_images": len(images),
                            "page_size": {
                                "width": page.rect.width,
                                "height": page.rect.height
                            }
                        }
                    }
                    
                    # 写入JSONL
                    jsonl_file.write(json.dumps(entry, ensure_ascii=False) + '\n')
                    
                    # 写入Markdown
                    md_file.write(f"## Page {page_num + 1}\n\n")
                    
                    # 写入文本内容
                    for block in content_blocks:
                        md_file.write(f"{block['content']}\n\n")
                    
                    # 写入表格
                    for table in tables:
                        md_file.write(f"{table['html_content']}\n\n")
                    
                    # 写入图片引用
                    for image in images:
                        md_file.write(f"![Image](imgs/{image['filename']})\n\n")
                    
                    logger.info(f"处理页面 {page_num + 1}/{len(doc)}")
                    
        except Exception as e:
            logger.error(f"处理异常: {str(e)}", exc_info=True)

def main():
    """主函数"""
    
    config = {
        "input_path": "./input_native",
        "output_dir": "./output_native",
        "filter_headers_footers": False,  # 完全不过滤，保持信息完整性
        "extract_tables": True,
        "extract_images": True,
        "language": "english",
        "debug": False  # 生产环境关闭调试
    }
    
    processor = NativePDFProcessor(config)
    
    input_path = Path(config["input_path"])
    if input_path.is_file():
        processor.process_pdf(str(input_path), config["output_dir"])
    else:
        pdf_files = list(input_path.glob("*.pdf"))
        logger.info(f"发现 {len(pdf_files)} 个PDF文件")
        
        for pdf_file in pdf_files:
            processor.process_pdf(str(pdf_file), config["output_dir"])

if __name__ == '__main__':
    main()

</code></pre>
            </div>

            <h3>技术栈</h3>
            <ul>
                <li><strong>扫描版PDF：</strong> PaddleOCR PPStructureV3</li>
                <li><strong>原生PDF：</strong> PyMuPDF</li>
                <li><strong>数据处理：</strong> pandas, json</li>
                <li><strong>开发语言：</strong> Python 3.10</li>
            </ul>

            <h3>主要成果</h3>
            <ul>
                <li>扫描版PDF文本识别准确率：<strong>96%</strong></li>
                <li>原生PDF文本提取完整度：<strong>80%</strong></li>
                <li>处理书籍数量：<strong>10本</strong>（每本21页测试样本）</li>
                <li>支持的文件格式：PDF（扫描版和原生版）</li>
            </ul>

            <h3>使用说明</h3>
            <ul>
                <li><strong>扫描版PDF处理：</strong>运行 <code>python processor.py</code>，需要安装PaddleOCR</li>
                <li><strong>原生PDF处理：</strong>运行 <code>python native.py</code>，需要安装PyMuPDF和pandas</li>
                <li><strong>输入文件：</strong>将PDF文件放入对应的input目录</li>
                <li><strong>输出结果：</strong>处理结果将保存在对应的output目录</li>
            </ul>
        </div>
    </div>
</body>
</html>