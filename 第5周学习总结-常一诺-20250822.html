<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>学习总结 (20250822)</title>
    <style>
        /* ===== 基础样式 ===== */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            margin: 0;
            padding: 0;
        }

        /* ===== 标题样式 ===== */
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
            font-weight: 600;
        }

        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 1.5em;
            scroll-margin-top: 20px; /* 锚点跳转时的顶部间距 */
        }

        h3 {
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 1.2em;
            scroll-margin-top: 20px; /* 锚点跳转时的顶部间距 */
        }

        /* ===== 容器布局 ===== */
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 40px 20px;
            background-color: #ffffff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* ===== 内容区块间距 ===== */
        .content {
            margin-bottom: 40px;
        }

        /* ===== 基本信息样式 ===== */
        .info {
            background-color: #f8f9fa;
            padding: 18px;
            border-radius: 6px;
            margin-bottom: 30px;
        }

        .info p {
            margin: 5px 0;
            font-size: 1.1em;
        }

        /* ===== 目录样式 ===== */
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .toc h2 {
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: none;
            color: #2c3e50;
            font-size: 1.3em;
        }

        .toc ul {
            list-style-type: none;
            padding-left: 0;
            margin: 0;
        }

        .toc > ul > li {
            margin-bottom: 12px;
            font-weight: 500;
        }

        .toc ul ul {
            padding-left: 25px;
            margin-top: 8px;
        }

        .toc ul ul li {
            margin-bottom: 6px;
            font-weight: normal;
        }

        .toc a {
            text-decoration: none;
            color: #2980b9;
            transition: color 0.2s ease;
        }

        .toc a:hover {
            color: #3498db;
            text-decoration: underline;
        }

        /* ===== 段落和列表样式 ===== */
        p {
            margin-bottom: 15px;
            text-align: justify;
        }

        ul, ol {
            margin-bottom: 15px;
            padding-left: 25px;
        }

        li {
            margin-bottom: 5px;
        }

        strong {
            color: #2c3e50;
            font-weight: 600;
        }

        /* ===== 代码样式 ===== */
        code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            color: #e74c3c;
        }

        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .code-block pre {
            margin: 0;
            padding: 20px;
            background: none;
            border: none;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #333;
        }

        .code-block code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: inherit;
            color: inherit;
        }

        /* ===== 链接样式 ===== */
        .github-link {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .github-link a {
            color: #0366d6;
            text-decoration: none;
            font-family: monospace;
            font-size: 14px;
            word-break: break-all;
        }

        .github-link a:hover {
            text-decoration: underline;
        }

        /* ===== 提示框样式 ===== */
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #f39c12;
        }

        .note p {
            margin: 0;
        }

        /* ===== 响应式设计 ===== */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px 15px;
            }

            h1 {
                font-size: 1.8em;
            }

            h2 {
                font-size: 1.3em;
            }

            .toc {
                padding: 15px;
            }
        }

        /* ===== 工具类：命令居中显示 ===== */
        .cmd-center {
            margin: 10px 0;
        }

        .cmd-center code {
            display: block;
            padding: 8px 16px; /* 增加内边距 */
            background-color: #f1f3f4; /* 与普通代码块相同的背景色 */
            color: #e74c3c; /* 与普通代码块相同的文字颜色 */
            border-radius: 4px; /* 与普通代码块相同的圆角 */
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            position: relative;
            cursor: pointer; /* 鼠标悬停显示手型 */
            transition: all 0.2s ease; /* 平滑过渡效果 */
            user-select: all; /* 方便选择文本 */
            border: 1px solid #e9ecef; /* 添加边框增强视觉效果 */
        }

        .cmd-center code:hover {
            background-color: #e9ecef; /* 悬停时稍微变暗 */
            border-color: #dee2e6; /* 悬停时边框颜色 */
        }

        .cmd-center code:active {
            background-color: #2a69ac; /* 点击时的反馈 */
        }

        /* 复制提示 */
        .cmd-center code::after {
            content: "点击复制";
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 10px;
            color: #6c757d; /* 适合浅色背景的灰色 */
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .cmd-center code:hover::after {
            opacity: 1;
        }

        /* 复制成功反馈 */
        .cmd-center code.copy-success {
            background-color: #d4edda !important; /* 浅绿色背景 */
            border-color: #c3e6cb !important; /* 绿色边框 */
            color: #155724 !important; /* 深绿色文字 */
        }

        .cmd-center code.copy-success::after {
            content: var(--after-content, "已复制!");
            opacity: 1 !important;
            color: #155724 !important; /* 深绿色提示文字 */
        }

    </style>

    <script>
        // 命令复制功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有命令块添加点击复制功能
            const cmdElements = document.querySelectorAll('.cmd-center code');

            cmdElements.forEach(function(element) {
                element.addEventListener('click', function() {
                    // 获取命令文本
                    const commandText = this.textContent.trim();

                    // 使用现代的 Clipboard API
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(commandText).then(function() {
                            showCopyFeedback(element, '已复制!');
                        }).catch(function(err) {
                            console.error('复制失败:', err);
                            fallbackCopyTextToClipboard(commandText, element);
                        });
                    } else {
                        // 降级方案
                        fallbackCopyTextToClipboard(commandText, element);
                    }
                });
            });

            // 降级复制方案（兼容旧浏览器）
            function fallbackCopyTextToClipboard(text, element) {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.position = "fixed";
                textArea.style.left = "-999999px";
                textArea.style.top = "-999999px";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        showCopyFeedback(element, '已复制!');
                    } else {
                        showCopyFeedback(element, '复制失败');
                    }
                } catch (err) {
                    console.error('降级复制失败:', err);
                    showCopyFeedback(element, '复制失败');
                }

                document.body.removeChild(textArea);
            }

            // 显示复制反馈
            function showCopyFeedback(element, message) {
                const originalAfter = element.style.getPropertyValue('--after-content');

                // 临时修改提示文字
                element.style.setProperty('--after-content', `"${message}"`);
                element.classList.add('copy-success');

                // 2秒后恢复原始状态
                setTimeout(function() {
                    element.style.removeProperty('--after-content');
                    element.classList.remove('copy-success');
                }, 2000);
            }
        });
    </script>
</head>

<body>
    <div class="container">
        <h1>学习总结</h1>

        <!-- 基本信息区域 -->
        <div class="info">
            <p><strong>姓名：</strong> 常一诺</p>
            <p><strong>日期：</strong> 2025年8月22日</p>
        </div>

        <!-- 目录导航 -->
        <div class="toc">
            <h2>目录</h2>

            <ul>
                <li><a href="#section1">一、语料库与知识图谱构建</a>
                    <ul>
                        <li><a href="#section1-1">1.1 精细化清洗</a></li>
                        <li><a href="#section1-2">1.2 medllm-finetune-rag</a></li>
                    </ul>
                </li>
                <li><a href="#section2">二、知识图谱理论学习</a>
                    <ul>
                        <li><a href="#section2-1">2.1 图算法与图数据分析</a></li>
                        <li><a href="#section2-2">2.2 知识图谱技术发展</a></li>
                    </ul>
                </li>
                <li><a href="#section3">三、文献阅读</a>
                    <ul>
                        <li><a href="#section3-1">3.1 Graph Retrieval-Augmented Generation: A Survey</a></li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- 主要内容区域 -->
        <div class="content">
            <h2 id="section1">一、语料库与知识图谱构建</h2>

            <h3 id="section1-1">1.1 精细化清洗</h3>
            <p>
                本周继续推进语料库的精细化清洗工作，开发了一个专门的清洗脚本（cleaner.py）用于处理文本。该脚本功能如下：
                <ul>
                    <li><strong>基础文本规范化</strong>
                        <ul>
                            <li>统一处理各种空白字符（包括换行、制表符、不间断空格等）</li>
                            <li>规范化Unicode字符（NFKC标准化）</li>
                            <li>统一处理破折号和范围符号</li>
                        </ul>
                    </li>
                    <li><strong>医学专业术语处理</strong>
                        <ul>
                            <li>LaTeX数学符号转换为Unicode字符（如 \pm → ±, \times → ×）</li>
                            <li>规范化单位表示（如 ml → mL, ug → μg）</li>
                            <li>统一给药途径表示（IV/IM/SC/PO/PR/SL）</li>
                            <li>规范化数字与单位之间的空格</li>
                        </ul>
                    </li>
                    <li><strong>OCR后处理优化</strong>
                        <ul>
                            <li>修复数字中被错误分割的小数点</li>
                            <li>处理重复斜杠问题</li>
                            <li>修正常见的OCR错误（如路径符号误识别）</li>
                        </ul>
                    </li>
                    <li><strong>文本格式化</strong>
                        <ul>
                            <li>统一项目符号格式</li>
                            <li>规范化标点符号周围的空格</li>
                            <li>支持生成清洗统计报告</li>
                        </ul>
                    </li>
                </ul>
                脚本如下：
                <div class="code-block">
                    <pre>
                        <code>
import json
import re
import os
import unicodedata
from pathlib import Path

ENABLE_REPORT = False if os.environ.get('CLEANER_REPORT', '').strip() == '' else True
ENABLE_M_BANG_FIX = False if os.environ.get('CLEANER_M_BANG', '').strip() == '' else True


def _inc(stats: dict | None, key: str, val: int):
    if stats is not None and val:
        stats[key] = stats.get(key, 0) + int(val)


def _normalize_whitespace(text: str, stats: dict | None = None) -> str:
    # 标准换行
    crlf = text.count('\r\n')
    _inc(stats, 'crlf_to_lf', crlf)
    text = text.replace('\r\n', '\n')
    lone_cr = text.count('\r')
    _inc(stats, 'cr_to_lf', lone_cr)
    text = text.replace('\r', '\n')
    # 替换各种空白为普通空格
    nbsp = text.count('\u00A0')
    thinsp = text.count('\u2009') + text.count('\u202F')
    tabs = text.count('\t')
    _inc(stats, 'nbsp_to_space', nbsp)
    _inc(stats, 'thinspace_to_space', thinsp)
    _inc(stats, 'tabs_to_space', tabs)
    text = text.replace('\u00A0', ' ').replace('\u2009', ' ').replace('\u202F', ' ')
    text = text.replace('\t', ' ')
    # 行尾空格去除
    stripped_lines = 0
    new_lines = []
    for line in text.split('\n'):
        if line != line.rstrip(' \t\u00A0'):
            stripped_lines += 1
        new_lines.append(line.rstrip(' \t\u00A0'))
    _inc(stats, 'trailing_ws_lines_trimmed', stripped_lines)
    text = '\n'.join(new_lines)
    # 合并重复空格（保留单个）
    text, n = re.subn(r' {2,}', ' ', text)
    _inc(stats, 'multi_space_collapses', n)
    return text


def _normalize_dashes_and_ranges(text: str, stats: dict | None = None) -> str:
    # 全角/异体连字符与范围符统一
    replacements = {
        '～': '~',
        '‑': '-',  # non-breaking hyphen
        '‐': '-',  # hyphen
        '‑': '-',
        '–': '-',  # en dash -> hyphen（范围后续仍保留 ~）
        '—\u200b': '—',  # em dash + zwsp
        '－': '-',
    }
    for k, v in replacements.items():
        cnt = text.count(k)
        _inc(stats, f'replaced_{ord(k[0]) if k else "dash"}', cnt)
        text = text.replace(k, v)
    # 将中文破折号附近多余空格收敛
    text, n = re.subn(r'\s*—\s*', '—', text)
    _inc(stats, 'em_dash_space_trim', n)
    return text


def _normalize_nfkc(text: str, stats: dict | None = None) -> str:
    new_text = unicodedata.normalize('NFKC', text)
    if new_text != text:
        _inc(stats, 'nfkc_normalized_lines', 1)
    return new_text


def _latex_to_unicode(text: str, stats: dict | None = None) -> str:
    # 去掉最外层数学定界符 $ ... $
    dollars = text.count('$')
    _inc(stats, 'latex_dollars_removed', dollars)
    text = text.replace('$$', '$$')  # 保留占位，后续去单个 $
    text = text.replace('$', '')

    # 常见 LaTeX 控制序列
    for old, new, key in [
        ('\\pm', '±', 'latex_pm'),
        ('\\times', '×', 'latex_times'),
        ('\\cdot', '·', 'latex_cdot'),
        ('\\sim', '~', 'latex_sim'),
        ('\\leq', '≤', 'latex_leq'),
        ('\\geq', '≥', 'latex_geq'),
        ('^{\\circ}C', '°C', 'latex_degC'),
        ('^{\\circ}F', '°F', 'latex_degF'),
        ('^\\circ C', '°C', 'latex_degC_alt'),
        ('^\\circ F', '°F', 'latex_degF_alt'),
        ('\\circ C', '°C', 'latex_degC_alt2'),
        ('\\circ F', '°F', 'latex_degF_alt2'),
    ]:
        cnt = text.count(old)
        _inc(stats, key, cnt)
        if cnt:
            text = text.replace(old, new)

    # \mu g / \mug -> μg
    # 单/双反斜杠 \mu g / \\mu g
    text, n_mu1 = re.subn(r'\\mu\s*g', 'μg', text)
    _inc(stats, 'latex_mu_g', n_mu1)
    text, n_mu1b = re.subn(r'\\\\mu\s*g', 'μg', text)
    _inc(stats, 'latex_mu_g_dbl', n_mu1b)
    # \mug / \\mug
    text, n_mu2 = re.subn(r'\\mug', 'μg', text)
    _inc(stats, 'latex_mug', n_mu2)
    text, n_mu2b = re.subn(r'\\\\mug', 'μg', text)
    _inc(stats, 'latex_mug_dbl', n_mu2b)

    # 去除 \mathrm{...} / \operatorname{...} / \text{...}
    def strip_braced_command(m: re.Match) -> str:
        inner = m.group(2)
        # 去内部多余空格
        inner = re.sub(r'\s+', ' ', inner)
        return inner

    text, n = re.subn(r'\\(mathrm|operatorname|text)\{([^}]*)\}', strip_braced_command, text)
    _inc(stats, 'latex_braced_cmds_stripped', n)

    # 下标数字转为 Unicode 下标（PGE_{2} -> PGE₂），也处理 _2
    sub_map = str.maketrans('0123456789+-=()', '₀₁₂₃₄₅₆₇₈₉₊₋₌₍₎')

    def replace_braced_sub(m: re.Match) -> str:
        base = m.group(1)
        sub = m.group(2).translate(sub_map)
        return f"{base}{sub}"

    def replace_simple_sub(m: re.Match) -> str:
        base = m.group(1)
        sub = m.group(2).translate(sub_map)
        return f"{base}{sub}"

    text, n1 = re.subn(r'([A-Za-z]{2,})_\{([0-9+\-=()]+)\}', replace_braced_sub, text)
    _inc(stats, 'subscripts_braced', n1)
    text, n2 = re.subn(r'([A-Za-z]{2,})_([0-9])\b', replace_simple_sub, text)
    _inc(stats, 'subscripts_simple', n2)

    return text


def _normalize_punctuation_spacing(text: str, stats: dict | None = None) -> str:
    # 英文标点后补空格：仅限英文字母/数字后接标点再接字母/数字的场景
    text, n1 = re.subn(r'([A-Za-z])([.,;:])(\S)', r'\1\2 \3', text)
    _inc(stats, 'punctuation_spacing_added', n1)
    # 句号后跟英文字母/数字时补空格
    text, n2 = re.subn(r'(\b[A-Za-z0-9])\.(?=[A-Za-z0-9])', r'\1. ', text)
    _inc(stats, 'period_spacing_added', n2)
    return text


def _normalize_units_and_routes(text: str, stats: dict | None = None) -> str:
    # 单位大小写与同形字
    text, n_ml = re.subn(r'\bml\b', 'mL', text)
    _inc(stats, 'unit_ml_to_mL', n_ml)
    text, n_ug = re.subn(r'\bug\b', 'μg', text)
    _inc(stats, 'unit_ug_to_μg', n_ug)
    lv_cnt = text.count('lV')
    _inc(stats, 'route_lV_to_IV', lv_cnt)
    text = text.replace('lV', 'IV')

    # 数字与单位之间加空格
    unit_pattern = re.compile(r'(\d+(?:\.\d+)?)(\s*)(mg|g|kg|μg|ng|mL|L|IU|U|mU|mmol|mol|cm|mm|μm|um|nm|mg/k g|mg/kg)', re.IGNORECASE)
    def unit_space(m: re.Match) -> str:
        return f"{m.group(1)} {m.group(3)}"
    text, n_units = re.subn(unit_pattern, unit_space, text)
    _inc(stats, 'unit_space_inserted', n_units)

    # 规范给药途径与频次：IV/IM/SC/PO/PR/SL 大写
    route_pattern = re.compile(r'\b(iv|im|sc|po|pr|sl)\b', flags=re.IGNORECASE)
    def upper_route(m: re.Match) -> str:
        return m.group(0).upper()
    text, n_routes = re.subn(route_pattern, upper_route, text)
    _inc(stats, 'routes_uppercased', n_routes)

    # 若单位后紧跟给药途径，补空格（如 1gPO -> 1 g PO）
    text, n_unit_route = re.subn(r'(\d+(?:\.\d+)?\s*(?:mg|g|kg|μg|ng|mL|L))(?=\s?(?:IV|IM|SC|PO|PR|SL)\b)',
                                 lambda m: re.sub(r'\s+', ' ', m.group(0)) + ' ', text)
    _inc(stats, 'unit_route_space_added', n_unit_route)

    # 频次与时间：q8h 之前保留空格（如 IV q8h）
    text, n_freq = re.subn(r'\b(IV|IM|SC|PO|PR|SL)\s*(?=q\d)', r'\1 ', text)
    _inc(stats, 'route_freq_space_added', n_freq)

    return text


def _fix_common_ocr(text: str, stats: dict | None = None) -> str:
    # 数字中被空格打断的小数：0. 12 -> 0.12
    text, n_dotspace = re.subn(r'(?<=\d)\.\s+(?=\d)', '.', text)
    _inc(stats, 'number_dot_space_fixed', n_dotspace)
    # 更通用的小数点后空格去除：. 5 -> .5
    text, n_dotspace2 = re.subn(r'(?<=\.)\s+(?=\d)', '', text)
    _inc(stats, 'number_dot_space_fixed_loose', n_dotspace2)

    # 斜杠重复：// -> /
    text, n_slashes = re.subn(r'/+', '/', text)
    _inc(stats, 'multi_slash_collapsed', n_slashes)

    # m! 误识别为 mL（可选）
    if ENABLE_M_BANG_FIX:
        text, n_ml1 = re.subn(r'(?<=/)\s*m!(?=\b)', 'mL', text)
        _inc(stats, 'unit_m!_to_mL_slash', n_ml1)
        text, n_ml2 = re.subn(r'(?<=\s)m!(?=\s|$|[.,;:])', 'mL', text)
        _inc(stats, 'unit_m!_to_mL_word', n_ml2)

    # 路径前的罗马数字变体与混排：1V/lV/IⅣ/IIV 等 -> IV；1M/lM -> IM
    text, n_route1 = re.subn(r'\b[1lI][Vv]\b', 'IV', text)
    _inc(stats, 'route_alt_to_IV_simple', n_route1)
    text, n_route1b = re.subn(r'\b[1lI]IV\b', 'IV', text)
    _inc(stats, 'route_alt_to_IV_IV', n_route1b)
    text, n_route2 = re.subn(r'\b[1lI][Mm]\b', 'IM', text)
    _inc(stats, 'route_alt_to_IM', n_route2)
    text, n_route3 = re.subn(r'\bIIV\b', 'IV', text)
    _inc(stats, 'route_IIV_to_IV', n_route3)
    # 仅写 /M 的情境视为 /IM（避免 /mg 等被误伤，使用词边界）
    text, n_route4 = re.subn(r'/(?:\s*)([Mm])\b', '/IM', text)
    _inc(stats, 'route_slash_M_to_IM', n_route4)

    # 路径后紧跟句点再频次：IV. q8h -> IV q8h
    text, n_route_dot = re.subn(r'\b(IV|IM|SC|PO|PR|SL)\s*\.\s*(?=q\d)', r'\1 ', text)
    _inc(stats, 'route_dot_before_freq_removed', n_route_dot)

    # mg/k g -> mg/kg
    text, n_mgkg = re.subn(r'mg/k\s*g', 'mg/kg', text, flags=re.IGNORECASE)
    _inc(stats, 'unit_mg_k_g_to_mg_kg', n_mgkg)

    # kg · d -> kg·d（单位中间点两侧空格收敛）
    text, n_kdotd = re.subn(r'kg\s*[·\.]\s*d', 'kg·d', text, flags=re.IGNORECASE)
    _inc(stats, 'unit_kg_dot_d', n_kdotd)

    return text


def _normalize_bullets(text: str, stats: dict | None = None) -> str:
    # 统一项目符号：将居中点替换为圆点+空格
    cnt = text.count('·')
    _inc(stats, 'bullet_dot_to_circle', cnt)
    text = text.replace('·', '• ')
    return text


def normalize_content(text: str, stats: dict | None = None) -> str:
    if not text:
        return text
    text = _normalize_whitespace(text, stats)
    text = _normalize_nfkc(text, stats)
    text = _normalize_dashes_and_ranges(text, stats)
    text = _latex_to_unicode(text, stats)
    text = _fix_common_ocr(text, stats)
    text = _normalize_units_and_routes(text, stats)
    text = _normalize_punctuation_spacing(text, stats)
    text = _normalize_bullets(text, stats)
    # 二次空白收敛
    text = _normalize_whitespace(text, stats)
    return text


def clean_single_jsonl(input_file_path: Path, output_file_path: Path) -> tuple[int, int, dict]:
    lines_read = 0
    lines_written = 0
    stats_total: dict = {}

    output_file_path.parent.mkdir(parents=True, exist_ok=True)

    with open(input_file_path, 'r', encoding='utf-8') as infile, \
         open(output_file_path, 'w', encoding='utf-8') as outfile:
        for line in infile:
            lines_read += 1
            try:
                obj = json.loads(line)
            except json.JSONDecodeError:
                # 跳过不可解析行
                if ENABLE_REPORT:
                    _inc(stats_total, 'json_decode_errors', 1)
                continue

            if isinstance(obj, dict) and isinstance(obj.get('content'), str):
                per_line_stats = {} if ENABLE_REPORT else None
                obj['content'] = normalize_content(obj['content'], per_line_stats)
                if ENABLE_REPORT and per_line_stats:
                    for k, v in per_line_stats.items():
                        stats_total[k] = stats_total.get(k, 0) + v

            outfile.write(json.dumps(obj, ensure_ascii=False) + '\n')
            lines_written += 1

    return lines_read, lines_written, stats_total


def process_directory(root_directory: str, output_root_directory: str):
    root_path = Path(root_directory)
    output_root_path = Path(output_root_directory)

    if not root_path.is_dir():
        print(f"错误：提供的输入路径 '{root_directory}' 不是一个有效的目录。")
        return

    print(f"--- 输入目录: {root_path.resolve()}")
    print(f"--- 输出目录: {output_root_path.resolve()}")

    output_root_path.mkdir(exist_ok=True)

    jsonl_files = [p for p in root_path.rglob('*.jsonl') if not p.name.endswith('_step1.jsonl')]

    if not jsonl_files:
        print('未找到需要处理的 .jsonl 文件。')
        return

    print(f"\n发现 {len(jsonl_files)} 个需要处理的 .jsonl 文件。\n")

    total = 0
    grand_stats: dict = {}
    for input_path in jsonl_files:
        relative_path = input_path.relative_to(root_path)
        output_path = output_root_path / relative_path
        output_path = output_path.with_name(f"{input_path.stem}_step1.jsonl")

        print(f"正在处理: {relative_path}")
        lines_read, lines_written, file_stats = clean_single_jsonl(input_path, output_path)
        if ENABLE_REPORT and file_stats:
            top_items = sorted(file_stats.items(), key=lambda x: x[1], reverse=True)[:6]
            summary = ', '.join([f"{k}:{v}" for k, v in top_items])
            print(f"  -> 统计: {summary}")
            for k, v in file_stats.items():
                grand_stats[k] = grand_stats.get(k, 0) + v
        print(f"  -> 规范化完成。读取 {lines_read} 行, 写入 {lines_written} 行到 '{output_path.relative_to(output_root_path)}'\n")
        total += 1

    print('--- 全部处理完毕 ---')
    print(f"总共成功处理了 {total} 个文件。结果保存在 '{output_root_directory}' 目录中。")
    if ENABLE_REPORT and grand_stats:
        print('\n=== 统计汇总（Top 12）===')
        for k, v in sorted(grand_stats.items(), key=lambda x: x[1], reverse=True)[:12]:
            print(f"{k}: {v}")


# 默认目录：以上一步输出作为本步输入
TARGET_DIRECTORY = 'scanned_jsonl_cleaned'
OUTPUT_DIRECTORY = 'scanned_jsonl_cleaned_step02'


if __name__ == '__main__':
    process_directory(TARGET_DIRECTORY, OUTPUT_DIRECTORY)
                        </code>
                    </pre>
                </div>
                理论上来说，该脚本是可以做到上述功能的，但是实际运行时还是遇到了一些小问题，比如关于单位和给药途径的匹配，关于LaTeX符号的转换等，都还需要进一步调试完善。
            </p>


            <h3 id="section1-2">1.2 medllm-finetune-rag</h3>
            <p>
                本次部署medllm-finetune-rag项目的过程经历了多个环境的尝试和多个问题的解决：<br><br>

                <strong>1. Windows环境尝试</strong><br>
                首先在Windows上尝试部署时，直接用requirements.txt安装各种依赖，但遇到依赖冲突和解析错误。随后尝试用shell脚本配置，但出现系统默认用文本编辑器打开文件而不是执行脚本的问题，于是决定改为在WSL上部署。<br><br>
                
                <strong>2. WSL环境配置</strong><br>
                在WSL上部署时，起初由于忘记核查python版本，直接在全局的python环境中安装依赖，结果因版本过低导致部署不成功。之后更换为python 3.10后再次运行shell脚本，成功配置好环境。但环境配置好后，又发现个人电脑的消费级GPU不支持模型运行，还是需要部署到服务器上。<br><br>
                
                <strong>3. 服务器部署挑战</strong><br>
                在服务器上部署时又出现了新的问题：原来在自己的本地执行环境配置的脚本时曾遇到网络问题，所以使用了clash tun模式，成功执行了环境配置脚本。但是由于访问服务器需要通过深信服的办公助手，这个办公助手会拦截它认为不安全的流量，所以tun失效了。之后换了github的代理也不OK。最后的解决方案是：先使用ssh协议从github上clone了出问题的unsloth仓库，然后在shell脚本中修改为从本地路径安装。<br><br>

                <strong>4. 技术要点总结</strong>
                <ul>
                    <li><strong>pip本地安装：</strong>这是第一次使用pip从本地安装包，使用命令<code>pip install -e "$HOME/unsloth[colab-new]"</code>。其中-e（Editable）参数的作用是创建符号链接而不是复制源代码到sitepackages目录，这样当本地代码更新时，无需重新安装即可生效。</li>
                    <li><strong>git操作技巧：</strong>在发现仓库代码更新后，学会了使用git stash保存本地修改，git pull更新代码，然后用git stash pop恢复修改的工作流程。</li>
                </ul>
                <br>
                目前遇到的新问题是项目的目录结构和readme文件中给的测试运行指导不匹配，需要进一步研究项目的运行和使用方法。
            </p>

            <h2 id="section2">二、知识图谱理论学习</h2>

            <h3 id="section2-1">2.1 图算法与图数据分析</h3>
            <h3 id="section2-1">2.1 图算法与图数据分析</h3>
            <p>
                本周系统学习了图算法与图数据分析的基础理论及其在知识图谱中的应用，核心收获如下：
                <ul>
                    <li>
                        <strong>图的基础知识：</strong>
                        <ul>
                            <li><strong>图的建模能力：</strong>认识到图作为一种强大的数据结构，能够有效建模现实世界中的复杂关系网络，如疾病交互、交通流量和社交关系等。</li>
                            <li> <strong>网络模型：</strong>学习了<strong>无尺度网络</strong>和<strong>随机网络</strong>等经典模型，理解了它们在模拟不同类型复杂系统中的作用。</li>
                            <li><strong>知识图谱与图的关系：</strong>明确了知识图谱是图的一种高度特化的应用，它不仅包含结构信息，更承载了丰富的语义，因此分析方法既可借鉴传统图算法，又需考虑其独特性。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>基础图算法：</strong>
                        <ul>
                            <li>学习了如图搜索、路径发现、中心度计算、社区发现等经典算法。这些算法是进行个体节点重要性评估和网络宏观结构分析的基石。</li>
                            <li>认识到传统图算法多依赖于符号匹配，随着技术发展，基于深度学习的<strong>表示学习</strong>方法已成为图挖掘分析的主流。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>图神经网络（GNN）与图表示学习：</strong>
                        <ul>
                            <li>梳理了图表示学习的发展脉络，从基于随机游走的模型（如 <strong>DeepWalk, node2vec, LINE</strong>）到真正引入神经网络思想的模型。</li>
                            <li>重点学习了主流的图神经网络模型，包括：
                                <ul>
                                    <li><strong>GCN (图卷积网络):</strong> 将卷积操作从图像域推广到图结构，聚合邻居节点信息。</li>
                                    <li><strong>GAT (图注意力网络):</strong> 引入注意力机制，为不同邻居分配不同权重。</li>
                                    <li><strong>GraphSAGE:</strong> 采用采样和聚合方式，实现了对大规模图的归纳式学习。</li>
                                </ul>
                            </li>
                            <li>了解了更前沿的模型，如处理异构图的 <strong>GTN</strong> 和结合预训练思想的 <strong>GPT-GNN, GCC</strong> 等。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>GNN与知识图谱的结合：</strong>
                        <ul>
                            <li>明确了GNN与传统知识图谱嵌入（KGE）方法的区别与联系。GNN更侧重于<strong>图的拓扑结构特征</strong>的学习，而KGE（如TransE）和规则学习更侧重于<strong>实体与关系的语义和逻辑特征</strong>。</li>
                            <li>一个高效的知识图谱表示学习方法，应是<strong>结构、语义、逻辑</strong>三者的有机结合，这也是未来的重要研究方向。</li>
                        </ul>
                    </li>
                </ul>
            </p>

            <h3 id="section2-2">2.2 知识图谱技术发展</h3>
             <p>
                关注了知识图谱领域的几个前沿技术发展方向：
                <ul>
                    <li>
                        <strong>多模态知识图谱：</strong>
                        <ul>
                            <li>知识图谱天然具备链接多源、异构数据的能力，其多模态特性是未来的必然趋势。通过图谱关联文本、图像、音视频等多模态数据，能够实现更深层次的语义理解。</li>
                            <li>核心价值在于发挥不同模态知识的<strong>互补性</strong>：一方面用多模态数据补全知识图谱，另一方面用知识图谱提升多模态任务（如多模态问答、推荐）的性能。</li>
                            <li>当前在多模态关系预测、实体对齐等方向仍有巨大的研究和创新空间。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>知识图谱与语言预训练模型的融合：</strong>
                        <ul>
                            <li>将外部知识注入预训练语言模型（PLM）是提升其事实性和推理能力的关键。主要有三类方法：
                                <ol>
                                    <li><strong>特征输入法：</strong>将知识图谱的表示向量作为额外特征输入模型 (e.g., ERNIE, KnowBERT)。</li>
                                    <li><strong>预训练任务法：</strong>设计新的、面向知识的预训练任务，隐式地将知识注入模型 (e.g., KEPLER, WKLM)。</li>
                                    <li><strong>模块增强法：</strong>通过增加适配器（Adapter）等额外模块来融合知识 (e.g., K-ADAPTER)。</li>
                                </ol>
                            </li>
                            <li>知识注入并非万能灵药，它对<strong>低资源任务</strong>和<strong>长尾实体</strong>的提升效果更显著，但也可能引入噪声，需要谨慎设计融合策略。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>事理知识图谱：</strong>
                        <ul>
                            <li>与传统的以实体为核心的知识图谱不同，事理图谱关注<strong>事件及其之间的逻辑关系</strong>（如因果、顺承、条件）。</li>
                            <li>如果说实体知识图谱是“静态的血肉”，那么事理图谱就是“动态的神经”，二者结合才能更全面地描述世界。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>知识图谱与低资源学习：</strong>
                        <ul>
                            <li>知识获取本身面临<strong>长尾问题</strong>，因此低资源学习是知识图谱自动化构建的关键技术。</li>
                            <li>反之，知识图谱作为高质量、结构化的先验知识，可以有效建模语义空间，为<strong>零样本、小样本</strong>等低资源学习任务提供重要支持。</li>
                        </ul>
                    </li>
                </ul>
            </p>

            <h2 id="section3">三、文献阅读</h2>

            <h3 id="section3-1">3.1 Graph Retrieval-Augmented Generation: A Survey</h3>
            <p>
                本周阅读了这篇关于图谱检索增强生成（GraphRAG）的综述论文，该文系统性地梳理了这一新兴领域。
                <ul>
                    <li>
                        <strong>论文概述：</strong>
                        <p>该论文是第一篇对 <strong>GraphRAG</strong> 技术的全面综述。其核心动机在于解决传统检索增强生成（RAG）的局限性。传统RAG主要从纯文本文档中检索信息，难以捕捉实体之间深层的、结构化的关系。GraphRAG通过从知识图谱（KG）中检索信息，能够更精确地利用实体间的复杂关系，从而有效缓解大型语言模型（LLM）的“幻觉”、知识过时和领域知识缺乏等问题。</p>
                    </li>
                    <li>
                        <strong>GraphRAG 核心工作流：</strong>
                        <p>论文将GraphRAG的工作流程形式化为三个核心阶段：</p>
                        <ol>
                            <li><strong>图索引 (G-Indexing):</strong> 这是初始阶段，旨在构建或选择一个与下游任务匹配的图数据库，并对其建立索引。图的来源可以是开放的知识图谱（如Wikidata），也可以是根据特定文档自构建的图。</li>
                            <li><strong>图引导检索 (G-Retrieval):</strong> 接收用户查询后，从图数据库中提取最相关的知识。检索的粒度可以是节点（实体）、三元组、路径或整个子图。这是GraphRAG区别于传统RAG的关键步骤。</li>
                            <li><strong>图增强生成 (G-Generation):</strong> 将检索到的结构化图信息与原始查询一同送入生成模型（通常是LLM），以生成更准确、更具上下文感知能力的回答。此阶段需要将图数据转换为LLM能够理解的格式（如自然语言描述、序列化格式等）。</li>
                        </ol>
                    </li>
                    <li>
                        <strong>关键技术与挑战：</strong>
                        <ul>
                            <li><strong>检索器类型：</strong>论文探讨了不同类型的检索器，包括基于启发式规则的非参数检索器、基于语言模型的检索器（LM-based）和基于图神经网络的检索器（GNN-based）。</li>
                            <li><strong>图格式化：</strong>如何将非欧几里得的图结构数据有效转换为LLM可处理的序列化格式是一个核心挑战。论文总结了多种方法，如邻接表、自然语言描述、代码形式（如GML）和节点序列等。</li>
                            <li><strong>工业应用与未来方向：</strong>论文还盘点了GraphRAG在工业界的初步应用（如微软、Neo4j的项目），并指出了未来的研究方向，包括动态图谱的集成、多模态信息融合、以及大规模图谱的高效检索机制等。</li>
                        </ul>
                    </li>
                </ul>
            </p>
        </div>
    </div>
</body>
</html>