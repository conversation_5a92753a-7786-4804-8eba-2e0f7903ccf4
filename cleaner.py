import json
import re
import os
import unicodedata
from pathlib import Path

ENABLE_REPORT = False if os.environ.get('CLEANER_REPORT', '').strip() == '' else True
ENABLE_M_BANG_FIX = False if os.environ.get('CLEANER_M_BANG', '').strip() == '' else True


def _inc(stats: dict | None, key: str, val: int):
    if stats is not None and val:
        stats[key] = stats.get(key, 0) + int(val)


def _normalize_whitespace(text: str, stats: dict | None = None) -> str:
    # 标准换行
    crlf = text.count('\r\n')
    _inc(stats, 'crlf_to_lf', crlf)
    text = text.replace('\r\n', '\n')
    lone_cr = text.count('\r')
    _inc(stats, 'cr_to_lf', lone_cr)
    text = text.replace('\r', '\n')
    # 替换各种空白为普通空格
    nbsp = text.count('\u00A0')
    thinsp = text.count('\u2009') + text.count('\u202F')
    tabs = text.count('\t')
    _inc(stats, 'nbsp_to_space', nbsp)
    _inc(stats, 'thinspace_to_space', thinsp)
    _inc(stats, 'tabs_to_space', tabs)
    text = text.replace('\u00A0', ' ').replace('\u2009', ' ').replace('\u202F', ' ')
    text = text.replace('\t', ' ')
    # 行尾空格去除
    stripped_lines = 0
    new_lines = []
    for line in text.split('\n'):
        if line != line.rstrip(' \t\u00A0'):
            stripped_lines += 1
        new_lines.append(line.rstrip(' \t\u00A0'))
    _inc(stats, 'trailing_ws_lines_trimmed', stripped_lines)
    text = '\n'.join(new_lines)
    # 合并重复空格（保留单个）
    text, n = re.subn(r' {2,}', ' ', text)
    _inc(stats, 'multi_space_collapses', n)
    return text


def _normalize_dashes_and_ranges(text: str, stats: dict | None = None) -> str:
    # 全角/异体连字符与范围符统一
    replacements = {
        '～': '~',
        '‑': '-',  # non-breaking hyphen
        '‐': '-',  # hyphen
        '‑': '-',
        '–': '-',  # en dash -> hyphen（范围后续仍保留 ~）
        '—\u200b': '—',  # em dash + zwsp
        '－': '-',
    }
    for k, v in replacements.items():
        cnt = text.count(k)
        _inc(stats, f'replaced_{ord(k[0]) if k else "dash"}', cnt)
        text = text.replace(k, v)
    # 将中文破折号附近多余空格收敛
    text, n = re.subn(r'\s*—\s*', '—', text)
    _inc(stats, 'em_dash_space_trim', n)
    return text


def _normalize_nfkc(text: str, stats: dict | None = None) -> str:
    new_text = unicodedata.normalize('NFKC', text)
    if new_text != text:
        _inc(stats, 'nfkc_normalized_lines', 1)
    return new_text


def _latex_to_unicode(text: str, stats: dict | None = None) -> str:
    # 去掉最外层数学定界符 $ ... $
    dollars = text.count('$')
    _inc(stats, 'latex_dollars_removed', dollars)
    text = text.replace('$$', '$$')  # 保留占位，后续去单个 $
    text = text.replace('$', '')

    # 常见 LaTeX 控制序列
    for old, new, key in [
        ('\\pm', '±', 'latex_pm'),
        ('\\times', '×', 'latex_times'),
        ('\\cdot', '·', 'latex_cdot'),
        ('\\sim', '~', 'latex_sim'),
        ('\\leq', '≤', 'latex_leq'),
        ('\\geq', '≥', 'latex_geq'),
        ('^{\\circ}C', '°C', 'latex_degC'),
        ('^{\\circ}F', '°F', 'latex_degF'),
        ('^\\circ C', '°C', 'latex_degC_alt'),
        ('^\\circ F', '°F', 'latex_degF_alt'),
        ('\\circ C', '°C', 'latex_degC_alt2'),
        ('\\circ F', '°F', 'latex_degF_alt2'),
    ]:
        cnt = text.count(old)
        _inc(stats, key, cnt)
        if cnt:
            text = text.replace(old, new)

    # \mu g / \mug -> μg
    # 单/双反斜杠 \mu g / \\mu g
    text, n_mu1 = re.subn(r'\\mu\s*g', 'μg', text)
    _inc(stats, 'latex_mu_g', n_mu1)
    text, n_mu1b = re.subn(r'\\\\mu\s*g', 'μg', text)
    _inc(stats, 'latex_mu_g_dbl', n_mu1b)
    # \mug / \\mug
    text, n_mu2 = re.subn(r'\\mug', 'μg', text)
    _inc(stats, 'latex_mug', n_mu2)
    text, n_mu2b = re.subn(r'\\\\mug', 'μg', text)
    _inc(stats, 'latex_mug_dbl', n_mu2b)

    # 去除 \mathrm{...} / \operatorname{...} / \text{...}
    def strip_braced_command(m: re.Match) -> str:
        inner = m.group(2)
        # 去内部多余空格
        inner = re.sub(r'\s+', ' ', inner)
        return inner

    text, n = re.subn(r'\\(mathrm|operatorname|text)\{([^}]*)\}', strip_braced_command, text)
    _inc(stats, 'latex_braced_cmds_stripped', n)

    # 下标数字转为 Unicode 下标（PGE_{2} -> PGE₂），也处理 _2
    sub_map = str.maketrans('0123456789+-=()', '₀₁₂₃₄₅₆₇₈₉₊₋₌₍₎')

    def replace_braced_sub(m: re.Match) -> str:
        base = m.group(1)
        sub = m.group(2).translate(sub_map)
        return f"{base}{sub}"

    def replace_simple_sub(m: re.Match) -> str:
        base = m.group(1)
        sub = m.group(2).translate(sub_map)
        return f"{base}{sub}"

    text, n1 = re.subn(r'([A-Za-z]{2,})_\{([0-9+\-=()]+)\}', replace_braced_sub, text)
    _inc(stats, 'subscripts_braced', n1)
    text, n2 = re.subn(r'([A-Za-z]{2,})_([0-9])\b', replace_simple_sub, text)
    _inc(stats, 'subscripts_simple', n2)

    return text


def _normalize_punctuation_spacing(text: str, stats: dict | None = None) -> str:
    # 英文标点后补空格：仅限英文字母/数字后接标点再接字母/数字的场景
    text, n1 = re.subn(r'([A-Za-z])([.,;:])(\S)', r'\1\2 \3', text)
    _inc(stats, 'punctuation_spacing_added', n1)
    # 句号后跟英文字母/数字时补空格
    text, n2 = re.subn(r'(\b[A-Za-z0-9])\.(?=[A-Za-z0-9])', r'\1. ', text)
    _inc(stats, 'period_spacing_added', n2)
    return text


def _normalize_units_and_routes(text: str, stats: dict | None = None) -> str:
    # 单位大小写与同形字
    text, n_ml = re.subn(r'\bml\b', 'mL', text)
    _inc(stats, 'unit_ml_to_mL', n_ml)
    text, n_ug = re.subn(r'\bug\b', 'μg', text)
    _inc(stats, 'unit_ug_to_μg', n_ug)
    lv_cnt = text.count('lV')
    _inc(stats, 'route_lV_to_IV', lv_cnt)
    text = text.replace('lV', 'IV')

    # 数字与单位之间加空格
    unit_pattern = re.compile(r'(\d+(?:\.\d+)?)(\s*)(mg|g|kg|μg|ng|mL|L|IU|U|mU|mmol|mol|cm|mm|μm|um|nm|mg/k g|mg/kg)', re.IGNORECASE)
    def unit_space(m: re.Match) -> str:
        return f"{m.group(1)} {m.group(3)}"
    text, n_units = re.subn(unit_pattern, unit_space, text)
    _inc(stats, 'unit_space_inserted', n_units)

    # 规范给药途径与频次：IV/IM/SC/PO/PR/SL 大写
    route_pattern = re.compile(r'\b(iv|im|sc|po|pr|sl)\b', flags=re.IGNORECASE)
    def upper_route(m: re.Match) -> str:
        return m.group(0).upper()
    text, n_routes = re.subn(route_pattern, upper_route, text)
    _inc(stats, 'routes_uppercased', n_routes)

    # 若单位后紧跟给药途径，补空格（如 1gPO -> 1 g PO）
    text, n_unit_route = re.subn(r'(\d+(?:\.\d+)?\s*(?:mg|g|kg|μg|ng|mL|L))(?=\s?(?:IV|IM|SC|PO|PR|SL)\b)',
                                 lambda m: re.sub(r'\s+', ' ', m.group(0)) + ' ', text)
    _inc(stats, 'unit_route_space_added', n_unit_route)

    # 频次与时间：q8h 之前保留空格（如 IV q8h）
    text, n_freq = re.subn(r'\b(IV|IM|SC|PO|PR|SL)\s*(?=q\d)', r'\1 ', text)
    _inc(stats, 'route_freq_space_added', n_freq)

    return text


def _fix_common_ocr(text: str, stats: dict | None = None) -> str:
    # 数字中被空格打断的小数：0. 12 -> 0.12
    text, n_dotspace = re.subn(r'(?<=\d)\.\s+(?=\d)', '.', text)
    _inc(stats, 'number_dot_space_fixed', n_dotspace)
    # 更通用的小数点后空格去除：. 5 -> .5
    text, n_dotspace2 = re.subn(r'(?<=\.)\s+(?=\d)', '', text)
    _inc(stats, 'number_dot_space_fixed_loose', n_dotspace2)

    # 斜杠重复：// -> /
    text, n_slashes = re.subn(r'/+', '/', text)
    _inc(stats, 'multi_slash_collapsed', n_slashes)

    # m! 误识别为 mL（可选）
    if ENABLE_M_BANG_FIX:
        text, n_ml1 = re.subn(r'(?<=/)\s*m!(?=\b)', 'mL', text)
        _inc(stats, 'unit_m!_to_mL_slash', n_ml1)
        text, n_ml2 = re.subn(r'(?<=\s)m!(?=\s|$|[.,;:])', 'mL', text)
        _inc(stats, 'unit_m!_to_mL_word', n_ml2)

    # 路径前的罗马数字变体与混排：1V/lV/IⅣ/IIV 等 -> IV；1M/lM -> IM
    text, n_route1 = re.subn(r'\b[1lI][Vv]\b', 'IV', text)
    _inc(stats, 'route_alt_to_IV_simple', n_route1)
    text, n_route1b = re.subn(r'\b[1lI]IV\b', 'IV', text)
    _inc(stats, 'route_alt_to_IV_IV', n_route1b)
    text, n_route2 = re.subn(r'\b[1lI][Mm]\b', 'IM', text)
    _inc(stats, 'route_alt_to_IM', n_route2)
    text, n_route3 = re.subn(r'\bIIV\b', 'IV', text)
    _inc(stats, 'route_IIV_to_IV', n_route3)
    # 仅写 /M 的情境视为 /IM（避免 /mg 等被误伤，使用词边界）
    text, n_route4 = re.subn(r'/(?:\s*)([Mm])\b', '/IM', text)
    _inc(stats, 'route_slash_M_to_IM', n_route4)

    # 路径后紧跟句点再频次：IV. q8h -> IV q8h
    text, n_route_dot = re.subn(r'\b(IV|IM|SC|PO|PR|SL)\s*\.\s*(?=q\d)', r'\1 ', text)
    _inc(stats, 'route_dot_before_freq_removed', n_route_dot)

    # mg/k g -> mg/kg
    text, n_mgkg = re.subn(r'mg/k\s*g', 'mg/kg', text, flags=re.IGNORECASE)
    _inc(stats, 'unit_mg_k_g_to_mg_kg', n_mgkg)

    # kg · d -> kg·d（单位中间点两侧空格收敛）
    text, n_kdotd = re.subn(r'kg\s*[·\.]\s*d', 'kg·d', text, flags=re.IGNORECASE)
    _inc(stats, 'unit_kg_dot_d', n_kdotd)

    return text


def _normalize_bullets(text: str, stats: dict | None = None) -> str:
    # 统一项目符号：将居中点替换为圆点+空格
    cnt = text.count('·')
    _inc(stats, 'bullet_dot_to_circle', cnt)
    text = text.replace('·', '• ')
    return text


def normalize_content(text: str, stats: dict | None = None) -> str:
    if not text:
        return text
    text = _normalize_whitespace(text, stats)
    text = _normalize_nfkc(text, stats)
    text = _normalize_dashes_and_ranges(text, stats)
    text = _latex_to_unicode(text, stats)
    text = _fix_common_ocr(text, stats)
    text = _normalize_units_and_routes(text, stats)
    text = _normalize_punctuation_spacing(text, stats)
    text = _normalize_bullets(text, stats)
    # 二次空白收敛
    text = _normalize_whitespace(text, stats)
    return text


def clean_single_jsonl(input_file_path: Path, output_file_path: Path) -> tuple[int, int, dict]:
    lines_read = 0
    lines_written = 0
    stats_total: dict = {}

    output_file_path.parent.mkdir(parents=True, exist_ok=True)

    with open(input_file_path, 'r', encoding='utf-8') as infile, \
         open(output_file_path, 'w', encoding='utf-8') as outfile:
        for line in infile:
            lines_read += 1
            try:
                obj = json.loads(line)
            except json.JSONDecodeError:
                # 跳过不可解析行
                if ENABLE_REPORT:
                    _inc(stats_total, 'json_decode_errors', 1)
                continue

            if isinstance(obj, dict) and isinstance(obj.get('content'), str):
                per_line_stats = {} if ENABLE_REPORT else None
                obj['content'] = normalize_content(obj['content'], per_line_stats)
                if ENABLE_REPORT and per_line_stats:
                    for k, v in per_line_stats.items():
                        stats_total[k] = stats_total.get(k, 0) + v

            outfile.write(json.dumps(obj, ensure_ascii=False) + '\n')
            lines_written += 1

    return lines_read, lines_written, stats_total


def process_directory(root_directory: str, output_root_directory: str):
    root_path = Path(root_directory)
    output_root_path = Path(output_root_directory)

    if not root_path.is_dir():
        print(f"错误：提供的输入路径 '{root_directory}' 不是一个有效的目录。")
        return

    print(f"--- 输入目录: {root_path.resolve()}")
    print(f"--- 输出目录: {output_root_path.resolve()}")

    output_root_path.mkdir(exist_ok=True)

    jsonl_files = [p for p in root_path.rglob('*.jsonl') if not p.name.endswith('_step1.jsonl')]

    if not jsonl_files:
        print('未找到需要处理的 .jsonl 文件。')
        return

    print(f"\n发现 {len(jsonl_files)} 个需要处理的 .jsonl 文件。\n")

    total = 0
    grand_stats: dict = {}
    for input_path in jsonl_files:
        relative_path = input_path.relative_to(root_path)
        output_path = output_root_path / relative_path
        output_path = output_path.with_name(f"{input_path.stem}_step1.jsonl")

        print(f"正在处理: {relative_path}")
        lines_read, lines_written, file_stats = clean_single_jsonl(input_path, output_path)
        if ENABLE_REPORT and file_stats:
            top_items = sorted(file_stats.items(), key=lambda x: x[1], reverse=True)[:6]
            summary = ', '.join([f"{k}:{v}" for k, v in top_items])
            print(f"  -> 统计: {summary}")
            for k, v in file_stats.items():
                grand_stats[k] = grand_stats.get(k, 0) + v
        print(f"  -> 规范化完成。读取 {lines_read} 行, 写入 {lines_written} 行到 '{output_path.relative_to(output_root_path)}'\n")
        total += 1

    print('--- 全部处理完毕 ---')
    print(f"总共成功处理了 {total} 个文件。结果保存在 '{output_root_directory}' 目录中。")
    if ENABLE_REPORT and grand_stats:
        print('\n=== 统计汇总（Top 12）===')
        for k, v in sorted(grand_stats.items(), key=lambda x: x[1], reverse=True)[:12]:
            print(f"{k}: {v}")


# 默认目录：以上一步输出作为本步输入
TARGET_DIRECTORY = 'scanned_jsonl_cleaned'
OUTPUT_DIRECTORY = 'scanned_jsonl_cleaned_step02'


if __name__ == '__main__':
    process_directory(TARGET_DIRECTORY, OUTPUT_DIRECTORY)