<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>学习总结 (20250801)</title>
    <style>
        /* ===== 基础样式 ===== */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            margin: 0;
            padding: 0;
        }

        /* ===== 标题样式 ===== */
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
            font-weight: 600;
        }

        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 1.5em;
            scroll-margin-top: 20px; /* 锚点跳转时的顶部间距 */
        }

        h3 {
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 1.2em;
            scroll-margin-top: 20px; /* 锚点跳转时的顶部间距 */
        }

        /* ===== 容器布局 ===== */
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 40px 20px;
            background-color: #ffffff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* ===== 内容区块间距 ===== */
        .content {
            margin-bottom: 40px;
        }

        /* ===== 基本信息样式 ===== */
        .info {
            background-color: #f8f9fa;
            padding: 18px;
            border-radius: 6px;
            margin-bottom: 30px;
        }

        .info p {
            margin: 5px 0;
            font-size: 1.1em;
        }

        /* ===== 目录样式 ===== */
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .toc h2 {
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: none;
            color: #2c3e50;
            font-size: 1.3em;
        }

        .toc ul {
            list-style-type: none;
            padding-left: 0;
            margin: 0;
        }

        .toc > ul > li {
            margin-bottom: 12px;
            font-weight: 500;
        }

        .toc ul ul {
            padding-left: 25px;
            margin-top: 8px;
        }

        .toc ul ul li {
            margin-bottom: 6px;
            font-weight: normal;
        }

        .toc a {
            text-decoration: none;
            color: #2980b9;
            transition: color 0.2s ease;
        }

        .toc a:hover {
            color: #3498db;
            text-decoration: underline;
        }

        /* ===== 段落和列表样式 ===== */
        p {
            margin-bottom: 15px;
            text-align: justify;
        }

        ul, ol {
            margin-bottom: 15px;
            padding-left: 25px;
        }

        li {
            margin-bottom: 5px;
        }

        strong {
            color: #2c3e50;
            font-weight: 600;
        }

        /* ===== 代码样式 ===== */
        code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            color: #e74c3c;
        }

        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .code-block pre {
            margin: 0;
            padding: 20px;
            background: none;
            border: none;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #333;
        }

        .code-block code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: inherit;
            color: inherit;
        }

        /* ===== 链接样式 ===== */
        .link {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .link a {
            color: #0366d6;
            text-decoration: none;
            font-family: monospace;
            font-size: 14px;
            word-break: break-all;
        }

        .link a:hover {
            text-decoration: underline;
        }

        /* ===== 提示框样式 ===== */
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #f39c12;
        }

        .note p {
            margin: 0;
        }

        /* ===== 响应式设计 ===== */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px 15px;
            }

            h1 {
                font-size: 1.8em;
            }

            h2 {
                font-size: 1.3em;
            }

            .toc {
                padding: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>学习总结</h1>

        <!-- 基本信息区域 -->
        <div class="info">
            <p><strong>姓名：</strong> 常一诺</p>
            <p><strong>日期：</strong> 2025年8月1日</p>
        </div>

        <!-- 目录导航 -->
        <div class="toc">
            <h2>目录</h2>

            <ul>
                <li><a href="#section1">一、书籍电子文本化</a>
                    <ul>
                        <li><a href="#section1-1">1.1 文档分类脚本编写</a></li>
                        <li><a href="#section1-2">1.2 扫描型PDF处理Python脚本改进</a></li>
                        <li><a href="#section1-3">1.3 下一步改进方向调研</a></li>
                        <!-- 复制li标签添加更多子章节 -->
                    </ul>
                </li>

                <li><a href="#section2">二、机器学习</a>
                    <ul>
                        <li><a href="#section2-1">2.1 支持向量机</a></li>
                        <li><a href="#section2-2">2.2 神经网络</a></li>
                        <li><a href="#section2-3">2.3 贝叶斯决策论</a></li>
                        <!-- 复制li标签添加更多子章节 -->
                    </ul>
                </li>

                <li><a href="#section3">三、知识图谱</a>
                    <ul>
                        <li><a href="#section3-1">3.1 知识图谱概论</a></li>
                        <li><a href="#section3-2">3.2 知识图谱的表示</a></li>
                        <li><a href="#section3-3">3.3 知识图谱的存储与查询</a></li>
                        <li><a href="#section3-4">3.4 知识图谱的抽取与构建</a></li>
                        <!-- 复制li标签添加更多子章节 -->
                    </ul>
                </li>

                <li><a href="#section4">四、文献阅读</a>
                    <ul>
                        <li><a href="#section4-1">4.1 A Theory of the Learnable</a></li>
                        <li><a href="#section4-2">4.2 Translating Embeddings for Modeling Multi-relational Data</a></li>
                        <!-- 复制li标签添加更多子章节 -->
                    </ul>
                </li>                
            </ul>
        </div>

        <!-- 主要内容区域 -->
        <div class="content">
            <h2 id="section1">一、书籍电子文本化</h2>

            <h3 id="section1-1">1.1 文档分类脚本编写</h3>
            <p>
                为提升PDF文档处理的自动化程度，开发了基于文本内容特征的PDF分类脚本。该脚本能够自动识别原生PDF和扫描版PDF，避免人工分类的低效率问题。
                <br>
                <strong>核心算法：</strong>通过分析PDF前几页的文本提取量，设定阈值判断文档类型。原生PDF通常包含可直接提取的文本流，而扫描版PDF的文本提取量极少或为零。
                <br>
                <strong>技术实现：</strong>使用PyMuPDF库进行文本提取，采用早停策略优化性能，并集成文件管理功能实现自动归档。
                <div class="code-block">
                    <pre>
                        <code>
# classifier.py
import fitz
import os
import shutil
from pathlib import Path

def classify_pdf(pdf_path, text_threshold=100, pages_to_check=3):
    """
    对单个PDF文件进行分类，判断其是'native'还是'scanned'
    """
    try:
        doc = fitz.open(pdf_path)
        total_text_length = 0
        num_pages = min(len(doc), pages_to_check)
        if num_pages == 0:
            doc.close()
            return 'empty'
        for page_num in range(num_pages):
            page = doc.load_page(page_num)
            total_text_length += len(page.get_text())
            if total_text_length > text_threshold:
                doc.close()
                return 'native'
        doc.close()
        return 'native' if total_text_length > text_threshold else 'scanned'
    except Exception as e:
        print(f"处理文件 {pdf_path} 时发生错误: {e}")
        return 'error'

def organize_pdfs_into_folders(source_dir, native_target_dir, scanned_target_dir):
    """
    扫描源目录，根据PDF类型将其移动到相应的目标目录。
    """
    print("--- PDF文件归类程序启动 ---")

    # --- 1. 检查并创建目录 ---
    source_path = Path(source_dir)
    native_path = Path(native_target_dir)
    scanned_path = Path(scanned_target_dir)

    if not source_path.is_dir():
        print(f"错误: 源目录 '{source_dir}' 不存在。请创建并放入PDF文件。")
        return

    # 使用 exist_ok=True, 即使目录已存在也不会报错
    native_path.mkdir(parents=True, exist_ok=True)
    scanned_path.mkdir(parents=True, exist_ok=True)
    print(f"源目录: {source_path}")
    print(f"原生PDF目标目录: {native_path}")
    print(f"扫描PDF目标目录: {scanned_path}\n")

    # --- 2. 获取所有PDF文件 ---
    pdf_files = list(source_path.glob("*.pdf"))
    if not pdf_files:
        print("在源目录中没有找到PDF文件。")
        return
        
    print(f"找到 {len(pdf_files)} 个PDF文件，开始分类和移动...\n")
    
    # --- 3. 遍历、分类和移动 ---
    counts = {"native": 0, "scanned": 0, "error": 0, "skipped": 0}
    for pdf_file in pdf_files:
        # a) 分类
        pdf_type = classify_pdf(str(pdf_file))
        
        # b) 根据分类结果确定目标路径
        destination_path = None
        if pdf_type == 'native':
            destination_path = native_path / pdf_file.name
            counts['native'] += 1
        elif pdf_type == 'scanned':
            destination_path = scanned_path / pdf_file.name
            counts['scanned'] += 1
        else:
            print(f"❗️ 文件 '{pdf_file.name}' 类型未知或处理错误，已跳过。")
            counts['error'] += 1
            continue

        # c) 检查目标位置是否已存在同名文件
        if destination_path.exists():
            print(f"🟡 文件 '{pdf_file.name}' 在目标目录已存在，已跳过。")
            counts['skipped'] += 1
            continue

        # d) 执行移动操作
        try:
            shutil.move(str(pdf_file), str(destination_path))
            print(f"✅ 已移动 '{pdf_file.name}' -> 到 '{destination_path.parent.name}' 目录")
        except Exception as e:
            print(f"❌ 移动文件 '{pdf_file.name}' 失败: {e}")
            counts[pdf_type] -= 1 # 移动失败，从计数中减去
            counts['error'] += 1

    # --- 4. 打印总结报告 ---
    print("\n--- 整理完成 ---")
    print(f"移动到 '原生' 目录: {counts['native']} 个")
    print(f"移动到 '扫描' 目录: {counts['scanned']} 个")
    print(f"因目标已存在而跳过: {counts['skipped']} 个")
    print(f"因错误而跳过: {counts['error']} 个")

if __name__ == '__main__':
    # --- 配置文件夹路径 ---
    # 1. 待分类的PDF
    SOURCE_DIRECTORY = './input_all'
    
    # 2. 分类后的原生PDF
    NATIVE_PDF_DIRECTORY = './inputs_native'
    
    # 3. 分类后的扫描PDF
    SCANNED_PDF_DIRECTORY = './inputs_scanned'
    
    # (如果文件夹不存在，会自动创建)
    if not os.path.exists(SOURCE_DIRECTORY):
        os.makedirs(SOURCE_DIRECTORY)
        print(f"创建了源文件夹 '{SOURCE_DIRECTORY}'。请将您要分类的PDF文件放入此文件夹，然后重新运行脚本。")
    else:
        organize_pdfs_into_folders(
            source_dir=SOURCE_DIRECTORY,
            native_target_dir=NATIVE_PDF_DIRECTORY,
            scanned_target_dir=SCANNED_PDF_DIRECTORY
        )
                        </code>
                    </pre>
                </div>
            </p>

            <h3 id="section1-2">1.2 扫描型PDF处理脚本优化</h3>
            <p>
                基于第1周的实践经验，对扫描版PDF处理脚本进行了系统性优化，主要改进包括：
                <ul>
                    <li><strong>性能优化：</strong>关闭印章识别功能，该功能在医学文献处理场景中非必需，关闭后可显著提升处理速度。</li>
                    <li><strong>图像处理完善：</strong>增加对OCR识别结果中图像内容的处理逻辑，确保Markdown文件中的图像引用路径有效。</li>
                    <li><strong>调试功能增强：</strong>新增布局分析可视化图像输出，便于分析模型的版面识别效果和调试处理流程。</li>
                    <li><strong>输出结构优化：</strong>建立层次化的输出目录结构，分别存储JSON数据、Markdown文档、提取图像和布局分析图。</li>
                </ul>
                <strong>技术架构：</strong>基于PaddleOCR PP-StructureV3模型，采用迭代处理方式控制内存占用，集成PIL进行图像处理。
                <div class="code-block">
                    <pre>
                        <code>
# scanned.py
import os
import json
from pathlib import Path
from paddleocr import PPStructureV3
from PIL import Image

def process_and_organize_outputs(input_dir="input", output_dir="output"):
    """
    批量处理PDF，并将不同类型的输出（JSON, Markdown, 提取的图片, 版面分析图）
    整理到结构清晰的目录中。
    """
    # --- 1. 初始化模型 ---
    print("正在初始化 PP-StructureV3 模型...")
    pipeline = PPStructureV3(
        use_doc_orientation_classify=True,  # 文档方向识别
        use_table_recognition=True,         # 表格识别
        use_doc_unwarping=False,            # 文档展平
        use_seal_recognition=False          # 印章识别
    )
    print("模型初始化完成。")

    # --- 2. 准备目录 ---
    if not os.path.exists(input_dir):
        print(f"错误：输入目录 '{input_dir}' 不存在。")
        return
    os.makedirs(output_dir, exist_ok=True)

    # --- 3. 遍历PDF ---
    pdf_files = [f for f in os.listdir(input_dir) if f.lower().endswith('.pdf')]
    if not pdf_files:
        print(f"在 '{input_dir}' 目录中没有找到PDF文件。")
        return
    
    print(f"找到 {len(pdf_files)} 个PDF文件待处理。")

    for filename in pdf_files:
        pdf_path = os.path.join(input_dir, filename)
        doc_name = Path(pdf_path).stem
        print(f"\n--- 开始处理文档: {filename} ---")

        doc_output_dir = os.path.join(output_dir, doc_name)
        os.makedirs(doc_output_dir, exist_ok=True)

        try:
            results_iterator = pipeline.predict_iter(input=pdf_path)
            
            all_pages_json_data = []
            all_pages_markdown_info = []
            page_count = 0
            
            print("开始逐页分析...")
            for page_result in results_iterator:
                page_count += 1
                print(f"  正在处理第 {page_count} 页...")
                
                # a) 聚合JSON和Markdown信息
                all_pages_json_data.append(page_result.json)
                markdown_info = page_result.markdown
                all_pages_markdown_info.append(markdown_info)
                
                # b) 保存从Markdown中提取的图片到 'imgs' 子目录
                if "markdown_images" in markdown_info:
                    for img_name, img_data in markdown_info["markdown_images"].items():
                        image_save_path = os.path.join(doc_output_dir, img_name)
                        image_dir = os.path.dirname(image_save_path)
                        os.makedirs(image_dir, exist_ok=True)
                        if isinstance(img_data, Image.Image):
                            img_data.save(image_save_path)
                
                # c) 保存版面分析可视化图片到 'layout_visualizations' 子目录
                vis_images = page_result.img
                if "layout_order_res" in vis_images:
                    vis_dir = os.path.join(doc_output_dir, "layout_visualizations")
                    os.makedirs(vis_dir, exist_ok=True)
                    vis_save_path = os.path.join(vis_dir, f"page_{page_count}.png")
                    vis_images["layout_order_res"].save(vis_save_path)
                    print(f"    - 已保存版面分析图: {vis_save_path}")

            print(f"文档共有 {page_count} 页。正在整合并保存最终文件...")
            json_save_path = os.path.join(doc_output_dir, f"{doc_name}.json")
            with open(json_save_path, 'w', encoding='utf-8') as f:
                json.dump(all_pages_json_data, f, ensure_ascii=False, indent=4)
            print(f"JSON 结果已保存到: {json_save_path}")

            full_markdown_content = pipeline.concatenate_markdown_pages(all_pages_markdown_info)
            md_save_path = os.path.join(doc_output_dir, f"{doc_name}.md")
            with open(md_save_path, 'w', encoding='utf-8') as f:
                f.write(full_markdown_content)
            print(f"Markdown 结果已保存到: {md_save_path}")
            print(f"--- 文档: {filename} 处理完成 ---")

        except Exception as e:
            print(f"处理文档 {filename} 时发生严重错误: {e}")
    print("\n所有任务已完成。")


if __name__ == "__main__":
    process_and_organize_outputs()
                        </code>
                    </pre>
                </div>
            </p>

            <h3 id="section1-3">1.3 技术改进方向调研</h3>
            <p>
                基于当前处理效果的分析，识别出两个主要的技术瓶颈并制定了相应的改进策略：
                <ul>
                    <li><strong>扫描版PDF复杂表格处理优化：</strong>
                        <ul>
                            <li><strong>现状分析：</strong>PP-StructureV3模型对简单结构表格处理效果良好，但在处理复杂结构表格（如《桑福德抗微生物治疗指南》）和无边框表格（如《哈里森感染病学》）时识别准确率下降。</li>
                            <li><strong>改进方案：</strong>针对无线表格，尝试切换到专门的预训练模型；同时通过实验调整关键参数，找到针对特定失败案例的最优配置，以提升识别效果。</li>
                        </ul>
                    </li>
                    <li><strong>原生PDF处理架构：</strong>
                        <ul>
                            <li><strong>问题描述：</strong>单纯使用PyMuPDF进行文本提取，对于编码不规范的复杂表格文档，输出结果呈现碎片化，缺乏结构信息。</li>
                            <li><strong>解决方案：</strong>第一阶段尝试统一使用OCR技术处理原生PDF，但会导致文本精度损失和效率低下的问题；第二阶段考虑混合使用LayoutParser进行版面分析，再用PyMuPDF提取文本进行填充，但LayoutParser无法解析单元格结构；最后阶段决定测试LayoutParser+PyMuPDF+TSR架构的效果。</li>
                            <li><strong>技术选型：</strong>LayoutParser用于版面分析，PyMuPDF用于文本提取，TSR用于表格识别。</li>
                        </ul>
                    </li>
                </ul>
            </p>
            
        </div>

        <div class="content">
            <h2 id="section2">二、机器学习</h2>

            <h3 id="section2-1">2.1 支持向量机 (Support Vector Machine)</h3>
            <p>
                支持向量机是一种重要的监督学习算法，在分类和回归任务中都有广泛应用。本周重点学习了SVM的核心概念和理论基础：
                <ul>
                    <li><strong>核心术语：</strong>
                        <ul>
                            <li><strong>间隔 (Margin)：</strong>样本点到分离超平面的距离，SVM目标是最大化间隔。</li>
                            <li><strong>支持向量：</strong>距离分离超平面最近的样本点，决定了超平面的位置。</li>
                            <li><strong>超平面方程：</strong>在n维空间中将数据分离的(n-1)维子空间。</li>
                            <li><strong>最大间隔：</strong>SVM的优化目标，寻找具有最大间隔的分离超平面。</li>
                        </ul>
                    </li>
                    <li><strong>模型求解：</strong>通过拉格朗日乘数法将原始优化问题转化为对偶问题求解。</li>
                    <li><strong>特征空间映射：</strong>当数据线性不可分时，通过映射函数将数据映射到高维特征空间。</li>
                    <li><strong>核函数：</strong>避免显式计算高维映射，常用核函数包括线性核、多项式核、RBF核等。</li>
                </ul>
                <strong>发展历程：</strong>从1963年Vapnik提出统计学习理论基础，到1995年软间隔SVM，再到1998年SMO算法，SVM经历了重要的理论和算法发展阶段。
            </p>

            <h3 id="section2-2">2.2 神经网络 (Neural Networks)</h3>
            <p>
                神经网络作为深度学习的基础，模拟了生物神经元的工作机制。学习内容包括：
                <ul>
                    <li><strong>基本概念：</strong>
                        <ul>
                            <li><strong>神经元模型：</strong>包含输入、权重、偏置、激活函数等基本组件。</li>
                            <li><strong>激活函数：</strong>引入非线性特性，常用函数包括Sigmoid、ReLU、Tanh等。</li>
                        </ul>
                    </li>
                    <li><strong>网络结构：</strong>
                        <ul>
                            <li><strong>多层前馈网络：</strong>信息从输入层经隐层传递到输出层，无反馈连接。</li>
                            <li><strong>隐层概念：</strong>位于输入层和输出层之间，负责特征提取和表示学习。</li>
                        </ul>
                    </li>
                    <li><strong>过拟合缓解策略：</strong>
                        <ul>
                            <li>正则化技术（L1/L2正则化）</li>
                            <li>Dropout技术</li>
                            <li>早停法 (Early Stopping)</li>
                            <li>数据增强</li>
                        </ul>
                    </li>
                </ul>
                <strong>发展回顾：</strong>从1940年代的感知机概念，经历1956-1969年的第一次热潮，1969年的低谷期，1984-1997年的复兴，到2012年至今的深度学习时代，神经网络经历了多次起伏发展。
            </p>

            <h3 id="section2-3">2.3 贝叶斯决策论 (Bayesian Decision Theory)</h3>
            <p>
                贝叶斯决策论为机器学习提供了重要的概率理论基础，是处理不确定性问题的核心方法：
                <ul>
                    <li><strong>理论基础：</strong>
                        <ul>
                            <li><strong>概率框架：</strong>在概率框架下实施最优决策的基本理论。</li>
                            <li><strong>贝叶斯判定准则：</strong>基于后验概率进行分类决策。</li>
                            <li><strong>贝叶斯定理：</strong>P(类别|特征) = P(特征|类别) × P(类别) / P(特征)。</li>
                        </ul>
                    </li>
                    <li><strong>模型类型：</strong>
                        <ul>
                            <li><strong>判别式模型：</strong>直接建模P(Y|X)，如逻辑回归、SVM。</li>
                            <li><strong>生成式模型：</strong>建模P(X,Y)或P(X|Y)和P(Y)，如朴素贝叶斯。</li>
                        </ul>
                    </li>
                    <li><strong>参数估计：</strong>
                        <ul>
                            <li><strong>极大似然估计：</strong>寻找使观测数据出现概率最大的参数值。</li>
                            <li><strong>朴素贝叶斯分类器：</strong>假设特征间条件独立的简化贝叶斯分类器。</li>
                            <li><strong>拉普拉斯修正：</strong>解决零概率问题，避免因某个概率为0导致整体概率为0。</li>
                        </ul>
                    </li>
                </ul>
                贝叶斯方法的优势在于能够自然地处理不确定性，并且可以随着新数据的到来不断更新模型的置信度。
            </p>

        </div>

        <div class="content">
            <h2 id="section3">三、知识图谱</h2>

            <h3 id="section3-1">3.1 知识图谱概论</h3>
            <p>
                知识图谱作为结构化知识表示的重要方法，在人工智能领域具有核心地位：
                <ul>
                    <li><strong>基本概念：</strong>
                        <ul>
                            <li>人类大脑依靠知识进行思考和推理，知识的表示、获取、学习和处理能力是人类心智的根本特征。</li>
                            <li>语言是知识的主要载体，语言与知识是实现认知智能的两个重要方面。</li>
                            <li>知识图谱是一种结构化的知识表示方法，相比文本更易于机器查询和处理。</li>
                        </ul>
                    </li>
                    <li><strong>核心基因：</strong>知识图谱融合了人工智能和互联网两大技术领域的优势。</li>
                    <li><strong>应用领域：</strong>
                        <ul>
                            <li>搜索引擎：提升搜索结果的语义理解和相关性。</li>
                            <li>智能问答：支持复杂问题的推理和回答。</li>
                            <li>大数据分析：通过语义融合多来源数据，支持复杂关联分析。</li>
                            <li>垂直领域：医疗、金融、电商、通信等领域的专业应用。</li>
                        </ul>
                    </li>
                    <li><strong>技术趋势：</strong>语言与知识的向量化表示，以及神经网络在知识处理中的应用。</li>
                </ul>
                <strong>重要观点：</strong>知识图谱不是单一技术，需要建立系统工程思维来统筹规划和实施。
            </p>

            <h3 id="section3-2">3.2 知识图谱的表示</h3>
            <p>
                知识表示是知识图谱的理论基础，涉及符号表示和神经网络两大技术路线：
                <ul>
                    <li><strong>表示方法演进：</strong>
                        <ul>
                            <li>传统方法：描述逻辑、Horn Logic、产生式规则、框架系统、语义网络。</li>
                            <li>现代方法：符号表示与神经网络相结合。</li>
                        </ul>
                    </li>
                    <li><strong>核心理念：</strong>
                        <ul>
                            <li>智能的精华在于推理能力，推理的关键是形式化逻辑。</li>
                            <li>学习需要明确学习目标，不是所有内容都可以通过学习获得。</li>
                            <li>大脑中的知识存储形式是Big Activity Vectors之间的相互作用。</li>
                        </ul>
                    </li>
                    <li><strong>发展挑战：</strong>
                        <ul>
                            <li>传统符号表示方法的共同缺点是知识获取过程主要依靠专家和人工。</li>
                            <li>越复杂的知识表示框架，知识获取过程越困难</li>
                            <li>需要在表达能力和获取难度之间找到平衡。</li>
                        </ul>
                    </li>
                </ul>
                知识表示与推理在人工智能发展历史上一直居于核心位置，是构建智能系统的理论基石。
            </p>

            <h3 id="section3-3">3.3 知识图谱的存储与查询</h3>
            <p>
                知识图谱的存储方案直接影响查询性能和系统可扩展性，需要根据应用场景选择合适的存储策略：
                <ul>
                    <li><strong>主要存储模型：</strong>
                        <ul>
                            <li><strong>属性图：</strong>工业界最常见的建模方法，性能优化充分，实用度高，但不支持符号推理。</li>
                            <li><strong>RDF：</strong>W3C推动的语义数据交换标准，有严格的语义逻辑基础，支持推理，兼容OWL。</li>
                            <li><strong>OWL：</strong>当三元组无法满足语义表示需要时，作为完备的本体语言提供更多语义表达构件。</li>
                        </ul>
                    </li>
                    <li><strong>存储方案对比：</strong>
                        <ul>
                            <li><strong>三元组表：</strong>结构简单但自连接操作开销巨大。</li>
                            <li><strong>水平表：</strong>类似邻接表，但可能超出列数限制且存在大量空值。</li>
                            <li><strong>属性表：</strong>克服自连接问题，但仍面临空值和多值属性问题。</li>
                            <li><strong>垂直划分：</strong>解决空值和多值问题，但需维护大量谓语表。</li>
                            <li><strong>六重索引：</strong>查询性能最优，但存储开销和维护代价高。</li>
                        </ul>
                    </li>
                    <li><strong>图数据库应用场景：</strong>
                        <ul>
                            <li>高性能关系查询：欺诈检测、社交网络分析、网络基础设施。</li>
                            <li>模型灵活性：链接元数据、版本控制、动态关系添加。</li>
                            <li>复杂分析规则：推荐系统、相似度计算、主数据管理。</li>
                        </ul>
                    </li>
                    <li><strong>选择原则：</strong>
                        <ul>
                            <li>区分原生图存储和非原生图存储：原生图存储在复杂关联查询方面有性能优势。</li>
                            <li>区分RDF图存储和属性图存储：RDF支持推理，属性图有更好的分析性能。</li>
                            <li>考虑与大数据存储引擎和图计算引擎的集成需求。</li>
                        </ul>
                    </li>
                </ul>
                <strong>核心观点：</strong>图模型更接近人脑认知和自然语言，是处理复杂、半结构化、多维度关联数据的最佳技术选择。
            </p>

            <h3 id="section3-4">3.4 知识图谱的抽取与构建</h3>
            <p>
                知识图谱的构建是新一代知识工程的核心任务，涉及实体识别、关系抽取、概念抽取和事件抽取等多个技术环节：
                <ul>
                    <li><strong>实体识别技术：</strong>
                        <ul>
                            <li><strong>算法发展：</strong>从HMM算法、鲍姆韦尔奇算法(EM算法)、维特比算法到CRF条件随机场模型。</li>
                            <li><strong>面临挑战：</strong>标签分布不平衡、实体嵌套等问题制约现实应用。</li>
                            <li><strong>中文特有问题：</strong>没有自然分词、用字变化多、简化表达现象严重。</li>
                            <li><strong>重要地位：</strong>是语义理解和构建知识图谱的重要环节，也是三元组抽取的前提基础。</li>
                        </ul>
                    </li>
                    <li><strong>关系抽取方法：</strong>
                        <ul>
                            <li><strong>基于模板：</strong>触发词匹配、依存句法匹配的关系抽取。</li>
                            <li><strong>监督学习：</strong>At-least-one Hypothesis等方法。</li>
                            <li><strong>机器学习框架：</strong>特征函数+最大熵模型、核函数、深度学习方法。</li>
                            <li><strong>技术演进：</strong>模板匹配→特征工程→远程监督→神经网络→降噪学习→预训练语言模型。</li>
                        </ul>
                    </li>
                    <li><strong>概念知识获取：</strong>
                        <ul>
                            <li><strong>定义：</strong>概念是人类从感性认识上升到理性认识，抽象出事物共同本质特点的表达。</li>
                            <li><strong>获取方法：</strong>基于模板、基于百科、基于序列标注等方法。</li>
                            <li><strong>应用价值：</strong>帮助自然语言理解，促进搜索、推荐等应用效果。</li>
                        </ul>
                    </li>
                    <li><strong>事件抽取：</strong>
                        <ul>
                            <li><strong>任务分解：</strong>事件发现和分类、事件要素抽取两部分。</li>
                            <li><strong>细分任务：</strong>触发词识别与事件分类、要素检测与要素角色分类。</li>
                            <li><strong>复杂性：</strong>比关系抽取更困难，事件结构远比实体关系三元组复杂。</li>
                            <li><strong>Schema约束：</strong>事件结构对事件抽取有很强的约束作用。</li>
                        </ul>
                    </li>
                    <li><strong>方法特点分析：</strong>
                        <ul>
                            <li><strong>模式匹配方法：</strong>特定领域性能好，便于理解，但覆盖度和可移植性差，高准确率低召回率。</li>
                            <li><strong>未来挑战：</strong>需要更智能的少样本、零样本知识抽取方法，以及能够终身学习的知识框架。</li>
                        </ul>
                    </li>
                </ul>
                <strong>核心理念：</strong>知识图谱≠专家系统，知识图谱就是新一代的知识工程，需要面对低资源场景和知识动态变化的挑战。
            </p>

        </div>

        <div class="content">
            <h2 id="section4">四、文献阅读</h2>

            <h3 id="section4-1">4.1 A Theory of the Learnable</h3>
            <p>
                L. G. Valiant 于 1984 年发表的论文《A Theory of the Learnable》被认为是机器学习领域的奠基性著作之一。它首次从计算角度正式定义了“学习”的含义，并提出了著名的 <strong>可能近似正确（PAC, Probably Approximately Correct）</strong> 学习模型。
                <ul>
                    <li>
                        <strong>核心思想：</strong>
                        在 Valiant 的框架中，学习被视为从数据中识别“概念”的过程。学习成功意味着找到了一个布尔函数，它能够以高概率近似正确地识别属于某一目标概念的输入。
                    </li>
                    <li>
                        <strong>模型结构：</strong>
                        <ul>
                            <li><strong>知识表示</strong>：使用布尔函数（如 CNF/DNF）表示概念。</li>
                            <li><strong>信息来源</strong>：
                            <ul>
                                <li><code>EXAMPLES</code>：正例样本，服从未知分布 D。</li>
                                <li><code>ORACLE</code>：可查询任意样本是否属于目标概念。</li>
                            </ul>
                            </li>
                            <li><strong>学习效率</strong>：算法必须在变量数量和精度参数（<code>1/ε</code>, <code>1/δ</code>）的多项式时间内完成。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>PAC 学习的定义：</strong>
                        一个概念类是 PAC 可学习的，若存在一个算法，在高概率（≥1−δ）下，能输出一个假设函数，使其在真实分布 D 下的误差小于 ε，且学习过程在多项式时间内完成。
                    </li>
                    <li>
                        <strong>可学习的布尔表达式类别：</strong>
                        <ul>
                            <li><strong>k-CNF</strong>：通过 EXAMPLES 即可学习。</li>
                            <li><strong>单调 DNF</strong>：需要结合 EXAMPLES 与 ORACLE。</li>
                            <li><strong>μ-表达式</strong>：每个变量仅出现一次，学习依赖更强的查询能力。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>理论启示与限制：</strong>
                        <ul>
                            <li>学习能力受到计算复杂性的限制，例如某些加密函数不可学习。</li>
                            <li>复杂概念需被“教师”分解为多个可学习的中间概念。</li>
                            <li>该理论将机器学习正式纳入了可分析的计算学科体系。</li>
                        </ul>
                    </li>
                </ul>
                该论文建立了现代机器学习的理论基础，对后续几十年的研究产生了深远的影响。
            </p>

            <h3 id="section4-2">4.2 Translating Embeddings for Modeling Multi-relational Data</h3>
            <p>
                本文发表于 2013 年 NIPS 大会，提出了一种用于知识图谱嵌入的新模型 TransE。它将实体和关系嵌入到同一个低维向量空间中，并将关系建模为实体之间的“平移操作”，即：若三元组 (h, l, t) 成立，则应满足向量关系 <code>h + l ≈ t</code>。
                <ul>
                    <li>
                        <strong>核心任务：</strong>
                        通过学习已有三元组间的模式，预测缺失链接，实现知识补全。
                    </li>
                    <li>
                        <strong>模型设计：</strong>
                        <ul>
                            <li>
                                <strong>关系平移：</strong>
                                TransE 假设每种关系都是实体向量间的一种平移。若 (h, l, t) 为真，则向量 <code>t</code> 应该靠近 <code>h + l</code>。
                            </li>
                            <li>
                                <strong>能量函数与损失函数：</strong>
                                <ul>
                                    <li>能量函数：<code>d(h + l, t)</code>，使用 L1 或 L2 范数。</li>
                                    <li>损失函数：基于间隔的排序损失，要求正例三元组的能量低于负例，且相差至少一个 margin。</li>
                                    <li>训练采用 SGD，并对实体向量施加 L2 范数归一化约束。</li>
                                </ul>
                            </li>
                            <li>
                                <strong>优势特点：</strong>
                                <ul>
                                    <li><strong>简洁高效：</strong> 参数量少，训练速度快，易扩展。</li>
                                    <li><strong>泛化能力强：</strong> 可快速学习新关系，适应大规模数据。</li>
                                    <li><strong>表现优异：</strong> 在多种关系类型（1对1、多对多等）上均表现出色。</li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                </ul>
                TransE 是知识图谱表示学习领域的奠基模型，提出了以向量平移建模关系的新思路，为后续一系列模型奠定了基础，并极大推动了知识图谱在问答系统、推荐系统等下游任务中的应用。
            </p>

        </div>

    </div>
</body>
</html>