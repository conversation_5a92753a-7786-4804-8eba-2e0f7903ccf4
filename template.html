<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>学习总结 ()</title>
    <style>
        /* ===== 基础样式 ===== */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            margin: 0;
            padding: 0;
        }

        /* ===== 标题样式 ===== */
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
            font-weight: 600;
        }

        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 1.5em;
            scroll-margin-top: 20px; /* 锚点跳转时的顶部间距 */
        }

        h3 {
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 1.2em;
            scroll-margin-top: 20px; /* 锚点跳转时的顶部间距 */
        }

        /* ===== 容器布局 ===== */
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 40px 20px;
            background-color: #ffffff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* ===== 内容区块间距 ===== */
        .content {
            margin-bottom: 40px;
        }

        /* ===== 基本信息样式 ===== */
        .info {
            background-color: #f8f9fa;
            padding: 18px;
            border-radius: 6px;
            margin-bottom: 30px;
        }

        .info p {
            margin: 5px 0;
            font-size: 1.1em;
        }

        /* ===== 目录样式 ===== */
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .toc h2 {
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: none;
            color: #2c3e50;
            font-size: 1.3em;
        }

        .toc ul {
            list-style-type: none;
            padding-left: 0;
            margin: 0;
        }

        .toc > ul > li {
            margin-bottom: 12px;
            font-weight: 500;
        }

        .toc ul ul {
            padding-left: 25px;
            margin-top: 8px;
        }

        .toc ul ul li {
            margin-bottom: 6px;
            font-weight: normal;
        }

        .toc a {
            text-decoration: none;
            color: #2980b9;
            transition: color 0.2s ease;
        }

        .toc a:hover {
            color: #3498db;
            text-decoration: underline;
        }

        /* ===== 段落和列表样式 ===== */
        p {
            margin-bottom: 15px;
            text-align: justify;
        }

        ul, ol {
            margin-bottom: 15px;
            padding-left: 25px;
        }

        li {
            margin-bottom: 5px;
        }

        strong {
            color: #2c3e50;
            font-weight: 600;
        }

        /* ===== 代码样式 ===== */
        code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            color: #e74c3c;
        }

        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .code-block pre {
            margin: 0;
            padding: 20px;
            background: none;
            border: none;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #333;
        }

        .code-block code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: inherit;
            color: inherit;
        }

        /* ===== 链接样式 ===== */
        .github-link {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .github-link a {
            color: #0366d6;
            text-decoration: none;
            font-family: monospace;
            font-size: 14px;
            word-break: break-all;
        }

        .github-link a:hover {
            text-decoration: underline;
        }

        /* ===== 提示框样式 ===== */
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #f39c12;
        }

        .note p {
            margin: 0;
        }

        /* ===== 响应式设计 ===== */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px 15px;
            }

            h1 {
                font-size: 1.8em;
            }

            h2 {
                font-size: 1.3em;
            }

            .toc {
                padding: 15px;
            }
        }

        /* ===== 工具类：命令居中显示 ===== */
        .cmd-center {
            margin: 10px 0;
        }

        .cmd-center code {
            display: block;
            padding: 8px 16px; /* 增加内边距 */
            background-color: #f1f3f4; /* 与普通代码块相同的背景色 */
            color: #e74c3c; /* 与普通代码块相同的文字颜色 */
            border-radius: 4px; /* 与普通代码块相同的圆角 */
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            position: relative;
            cursor: pointer; /* 鼠标悬停显示手型 */
            transition: all 0.2s ease; /* 平滑过渡效果 */
            user-select: all; /* 方便选择文本 */
            border: 1px solid #e9ecef; /* 添加边框增强视觉效果 */
        }

        .cmd-center code:hover {
            background-color: #e9ecef; /* 悬停时稍微变暗 */
            border-color: #dee2e6; /* 悬停时边框颜色 */
        }

        .cmd-center code:active {
            background-color: #2a69ac; /* 点击时的反馈 */
        }

        /* 复制提示 */
        .cmd-center code::after {
            content: "点击复制";
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 10px;
            color: #6c757d; /* 适合浅色背景的灰色 */
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .cmd-center code:hover::after {
            opacity: 1;
        }

        /* 复制成功反馈 */
        .cmd-center code.copy-success {
            background-color: #d4edda !important; /* 浅绿色背景 */
            border-color: #c3e6cb !important; /* 绿色边框 */
            color: #155724 !important; /* 深绿色文字 */
        }

        .cmd-center code.copy-success::after {
            content: var(--after-content, "已复制!");
            opacity: 1 !important;
            color: #155724 !important; /* 深绿色提示文字 */
        }

    </style>

    <script>
        // 命令复制功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有命令块添加点击复制功能
            const cmdElements = document.querySelectorAll('.cmd-center code');

            cmdElements.forEach(function(element) {
                element.addEventListener('click', function() {
                    // 获取命令文本
                    const commandText = this.textContent.trim();

                    // 使用现代的 Clipboard API
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(commandText).then(function() {
                            showCopyFeedback(element, '已复制!');
                        }).catch(function(err) {
                            console.error('复制失败:', err);
                            fallbackCopyTextToClipboard(commandText, element);
                        });
                    } else {
                        // 降级方案
                        fallbackCopyTextToClipboard(commandText, element);
                    }
                });
            });

            // 降级复制方案（兼容旧浏览器）
            function fallbackCopyTextToClipboard(text, element) {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.position = "fixed";
                textArea.style.left = "-999999px";
                textArea.style.top = "-999999px";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        showCopyFeedback(element, '已复制!');
                    } else {
                        showCopyFeedback(element, '复制失败');
                    }
                } catch (err) {
                    console.error('降级复制失败:', err);
                    showCopyFeedback(element, '复制失败');
                }

                document.body.removeChild(textArea);
            }

            // 显示复制反馈
            function showCopyFeedback(element, message) {
                const originalAfter = element.style.getPropertyValue('--after-content');

                // 临时修改提示文字
                element.style.setProperty('--after-content', `"${message}"`);
                element.classList.add('copy-success');

                // 2秒后恢复原始状态
                setTimeout(function() {
                    element.style.removeProperty('--after-content');
                    element.classList.remove('copy-success');
                }, 2000);
            }
        });
    </script>
</head>

<body>
    <div class="container">
        <h1>学习总结</h1>

        <!-- 基本信息区域 -->
        <div class="info">
            <p><strong>姓名：</strong> 常一诺</p>
            <p><strong>日期：</strong> YYYY年MM月DD日</p>
        </div>

        <!-- 目录导航 -->
        <div class="toc">
            <h2>目录</h2>

            <ul>
                <li><a href="#section1">一、[章节标题]</a>
                    <ul>
                        <li><a href="#section1-1">1.1 [子章节标题]</a></li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- 主要内容区域 -->
        <div class="content">
            <h2 id="section1">一、[章节标题]</h2>

            <h3 id="section1-1">1.1 [子章节标题]</h3>
            <p>
                [在这里填写具体内容]
            </p>
        </div>
    </div>
</body>
</html>