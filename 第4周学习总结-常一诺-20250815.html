<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>学习总结 (20250815)</title>
    <style>
        /* ===== 基础样式 ===== */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            margin: 0;
            padding: 0;
        }

        /* ===== 标题样式 ===== */
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
            font-weight: 600;
        }

        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 1.5em;
            scroll-margin-top: 20px; /* 锚点跳转时的顶部间距 */
        }

        h3 {
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 1.2em;
            scroll-margin-top: 20px; /* 锚点跳转时的顶部间距 */
        }

        /* ===== 容器布局 ===== */
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 40px 20px;
            background-color: #ffffff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* ===== 内容区块间距 ===== */
        .content {
            margin-bottom: 40px;
        }

        /* ===== 基本信息样式 ===== */
        .info {
            background-color: #f8f9fa;
            padding: 18px;
            border-radius: 6px;
            margin-bottom: 30px;
        }

        .info p {
            margin: 5px 0;
            font-size: 1.1em;
        }

        /* ===== 目录样式 ===== */
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .toc h2 {
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: none;
            color: #2c3e50;
            font-size: 1.3em;
        }

        .toc ul {
            list-style-type: none;
            padding-left: 0;
            margin: 0;
        }

        .toc > ul > li {
            margin-bottom: 12px;
            font-weight: 500;
        }

        .toc ul ul {
            padding-left: 25px;
            margin-top: 8px;
        }

        .toc ul ul li {
            margin-bottom: 6px;
            font-weight: normal;
        }

        .toc a {
            text-decoration: none;
            color: #2980b9;
            transition: color 0.2s ease;
        }

        .toc a:hover {
            color: #3498db;
            text-decoration: underline;
        }

        /* ===== 段落和列表样式 ===== */
        p {
            margin-bottom: 15px;
            text-align: justify;
        }

        ul, ol {
            margin-bottom: 15px;
            padding-left: 25px;
        }

        li {
            margin-bottom: 5px;
        }

        strong {
            color: #2c3e50;
            font-weight: 600;
        }

        /* ===== 代码样式 ===== */
        code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            color: #e74c3c;
        }

        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .code-block pre {
            margin: 0;
            padding: 20px;
            background: none;
            border: none;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #333;
        }

        .code-block code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: inherit;
            color: inherit;
        }

        /* ===== 链接样式 ===== */
        .github-link {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .github-link a {
            color: #0366d6;
            text-decoration: none;
            font-family: monospace;
            font-size: 14px;
            word-break: break-all;
        }

        .github-link a:hover {
            text-decoration: underline;
        }

        /* ===== 提示框样式 ===== */
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #f39c12;
        }

        .note p {
            margin: 0;
        }

        /* ===== 响应式设计 ===== */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px 15px;
            }

            h1 {
                font-size: 1.8em;
            }

            h2 {
                font-size: 1.3em;
            }

            .toc {
                padding: 15px;
            }
        }

        /* ===== 工具类：命令居中显示 ===== */
        .cmd-center {
            margin: 10px 0;
        }

        .cmd-center code {
            display: block;
            padding: 8px 16px; /* 增加内边距 */
            background-color: #f1f3f4; /* 与普通代码块相同的背景色 */
            color: #e74c3c; /* 与普通代码块相同的文字颜色 */
            border-radius: 4px; /* 与普通代码块相同的圆角 */
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            position: relative;
            cursor: pointer; /* 鼠标悬停显示手型 */
            transition: all 0.2s ease; /* 平滑过渡效果 */
            user-select: all; /* 方便选择文本 */
            border: 1px solid #e9ecef; /* 添加边框增强视觉效果 */
        }

        .cmd-center code:hover {
            background-color: #e9ecef; /* 悬停时稍微变暗 */
            border-color: #dee2e6; /* 悬停时边框颜色 */
        }

        .cmd-center code:active {
            background-color: #2a69ac; /* 点击时的反馈 */
        }

        /* 复制提示 */
        .cmd-center code::after {
            content: "点击复制";
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 10px;
            color: #6c757d; /* 适合浅色背景的灰色 */
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .cmd-center code:hover::after {
            opacity: 1;
        }

        /* 复制成功反馈 */
        .cmd-center code.copy-success {
            background-color: #d4edda !important; /* 浅绿色背景 */
            border-color: #c3e6cb !important; /* 绿色边框 */
            color: #155724 !important; /* 深绿色文字 */
        }

        .cmd-center code.copy-success::after {
            content: var(--after-content, "已复制!");
            opacity: 1 !important;
            color: #155724 !important; /* 深绿色提示文字 */
        }

    </style>

    <script>
        // 命令复制功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有命令块添加点击复制功能
            const cmdElements = document.querySelectorAll('.cmd-center code');

            cmdElements.forEach(function(element) {
                element.addEventListener('click', function() {
                    // 获取命令文本
                    const commandText = this.textContent.trim();

                    // 使用现代的 Clipboard API
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(commandText).then(function() {
                            showCopyFeedback(element, '已复制!');
                        }).catch(function(err) {
                            console.error('复制失败:', err);
                            fallbackCopyTextToClipboard(commandText, element);
                        });
                    } else {
                        // 降级方案
                        fallbackCopyTextToClipboard(commandText, element);
                    }
                });
            });

            // 降级复制方案（兼容旧浏览器）
            function fallbackCopyTextToClipboard(text, element) {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.position = "fixed";
                textArea.style.left = "-999999px";
                textArea.style.top = "-999999px";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        showCopyFeedback(element, '已复制!');
                    } else {
                        showCopyFeedback(element, '复制失败');
                    }
                } catch (err) {
                    console.error('降级复制失败:', err);
                    showCopyFeedback(element, '复制失败');
                }

                document.body.removeChild(textArea);
            }

            // 显示复制反馈
            function showCopyFeedback(element, message) {
                const originalAfter = element.style.getPropertyValue('--after-content');

                // 临时修改提示文字
                element.style.setProperty('--after-content', `"${message}"`);
                element.classList.add('copy-success');

                // 2秒后恢复原始状态
                setTimeout(function() {
                    element.style.removeProperty('--after-content');
                    element.classList.remove('copy-success');
                }, 2000);
            }
        });
    </script>
</head>

<body>
    <div class="container">
        <h1>学习总结</h1>

        <!-- 基本信息区域 -->
        <div class="info">
            <p><strong>姓名：</strong> 常一诺</p>
            <p><strong>日期：</strong> 2025年8月15日</p>
        </div>

        <!-- 目录导航 -->
        <div class="toc">
            <h2>目录</h2>

            <ul>
                <li><a href="#section1">一、书籍电子文本化</a>
                    <ul>
                        <li><a href="#section1-1">1.1 书籍文本提取</a></li>
                    </ul>
                </li>

                <li><a href="#section2">二、语料库与知识图谱建立</a>
                    <ul>
                        <li><a href="#section2-1">2.1 文本初步清洗</a></li>
                    </ul>
                    <ul>
                        <li><a href="#section2-2">2.2 文本精细清洗方案调研</a></li>
                    </ul>
                    <ul>
                        <li><a href="#section2-3">2.3 知识抽取关键技术调研</a></li>
                    </ul>
                </li>

                <li><a href="#section3">三、机器学习理论学习</a>
                    <ul>
                        <li><a href="#section3-1">3.1 假设空间</a></li>
                    </ul>
                    <ul>
                        <li><a href="#section3-2">3.2 NFL定理推导</a></li>
                    </ul>
                    <ul>
                        <li><a href="#section3-3">3.3 PR指标的扩展</a></li>
                    </ul>
                    <ul>
                        <li><a href="#section3-4">3.4 ROC与AUC</a></li>
                    </ul>
                    <ul>
                        <li><a href="#section3-5">3.5 非均等代价</a></li>
                    </ul>
                    <ul>
                        <li><a href="#section3-6">3.6 偏差-方差分解</a></li>
                    </ul>
                </li>

                <li><a href="#section4">四、知识图谱理论学习</a>
                    <ul>
                        <li><a href="#section4-1">4.1 知识图谱问答</a></li>
                    </ul>
                </li>

                <li><a href="#section5">五、文献阅读</a>
                    <ul>
                        <li><a href="#section5-1">5.1 Self-supervised Quantized Representation for Seamlessly Integrating Knowledge Graphs with Large Language Models</a></li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- 主要内容区域 -->
        <div class="content">
            <h2 id="section1">一、书籍电子文本化</h2>
            <h3 id="section1-1">1.1 书籍文本提取</h3>
            <p>
                <ul>
                    <li>
                        完成了《Harrison’s Infectious Diseases》和《Manual of Clinical Microbiology》的整书内容提取。
                    </li>
                    <li>
                        由于《桑福德抗微生物治疗指南》的复杂性，暂时放弃对其内容的提取。
                    </li>
                    <li>
                        对于《ABX Guide》这本书，可以考虑订阅网站后爬取网页内容，但操作不当很可能会面临账号封禁的问题。所以这本书也先暂时放一下。
                    </li>
                </ul>
            </p>

            <h2 id="section2">二、语料库与知识图谱建立</h2>
            <h3 id="section2-1">2.1 文本初步清洗</h3>
            <p>
                对于扫描型PDF初步提取出的内容，有很多冗余的内容——页眉、页脚、侧边内容、图片等，可以通过脚本批量去除。此外，用不到的字段——文本块类型置信度、文本块坐标、图片路径等，也可以去除，减小数据量。脚本如下：
                <div class="code-block">
                    <pre>
                        <code>
# cleaner.py
import json
from pathlib import Path

def clean_and_slim_single_jsonl(input_file_path, output_file_path):
    """
    读取一个jsonl文件，执行过滤操作。
    """
    block_types_to_remove = {'header', 'number', 'aside_text'}
    fields_to_keep = [
        'id', 
        'source_doc', 
        'page_num', 
        'block_type', 
        'content', 
        'content_confidence',
        'reading_order'
    ]
    
    lines_read = 0
    lines_written = 0
    
    try:
        # 在写入前，确保输出文件的父目录存在
        output_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(input_file_path, 'r', encoding='utf-8') as infile, \
             open(output_file_path, 'w', encoding='utf-8') as outfile:
            
            for line in infile:
                lines_read += 1
                try:
                    data = json.loads(line)
                    if data.get('block_type') not in block_types_to_remove:
                        filtered_data = {key: data.get(key) for key in fields_to_keep}
                        outfile.write(json.dumps(filtered_data, ensure_ascii=False) + '\n')
                        lines_written += 1
                except json.JSONDecodeError:
                    print(f"    - 警告：在文件 {input_file_path.name} 中跳过无法解析的行: {line.strip()}")
                    
    except FileNotFoundError:
        print(f"错误：输入文件 '{input_file_path}' 未找到。")
    except Exception as e:
        print(f"处理文件 '{input_file_path}' 时发生错误: {e}")
        
    return lines_read, lines_written

def process_directory(root_directory, output_root_directory):
    """
    递归遍历输入目录，并将处理后的文件保存到独立的输出目录中，
    同时保持原始的目录结构。
    """
    root_path = Path(root_directory)
    output_root_path = Path(output_root_directory)
    
    if not root_path.is_dir():
        print(f"错误：提供的输入路径 '{root_directory}' 不是一个有效的目录。")
        return

    print(f"--- 输入目录: {root_path.resolve()}")
    print(f"--- 输出目录: {output_root_path.resolve()}")
    
    # 确保顶层输出目录存在
    output_root_path.mkdir(exist_ok=True)
    
    jsonl_files_to_process = [
        path for path in root_path.rglob('*.jsonl') 
        if not path.name.endswith('_cleaned.jsonl')
    ]

    if not jsonl_files_to_process:
        print("未找到需要处理的 .jsonl 文件。")
        return
        
    print(f"\n发现 {len(jsonl_files_to_process)} 个需要处理的 .jsonl 文件。\n")
    
    total_files_processed = 0
    
    for input_path in jsonl_files_to_process:
        # 构建与输入平行的输出路径
        relative_path = input_path.relative_to(root_path)
        output_path = output_root_path / relative_path

        # 修改输出文件名，添加 '_cleaned' 后缀
        output_path = output_path.with_name(f"{input_path.stem}_cleaned.jsonl")

        print(f"正在处理: {relative_path}")
        
        lines_read, lines_written = clean_and_slim_single_jsonl(input_path, output_path)
        
        print(f"  -> 清理完成。读取 {lines_read} 行, 写入 {lines_written} 行到 '{output_path.relative_to(output_root_path)}'\n")
        total_files_processed += 1
        
    print(f"--- 全部处理完毕 ---")
    print(f"总共成功处理了 {total_files_processed} 个文件。结果保存在 '{output_root_directory}' 目录中。")


TARGET_DIRECTORY = 'scanned' 
OUTPUT_DIRECTORY = 'scanned_jsonl_cleaned'

# 运行主函数
if __name__ == "__main__":
    # 安全检查
    if 'path/to/your' in TARGET_DIRECTORY:
        print("！！！请先修改脚本中的 TARGET_DIRECTORY 变量，使其指向您的书籍根目录。！！！")
    else:
        process_directory(TARGET_DIRECTORY, OUTPUT_DIRECTORY)
                        </code>
                    </pre>
                </div>
                该脚本只是对jsonl文件进行初步清洗，后续还需要更精细的清洗工作。
            </p>
            <h3 id="section2-2">2.2 文本精细清洗方案调研</h3>
            <p>
                精细清洗主要涉及OCR错误修正。使用纯人工的方式成本高、效率低，考虑使用LLM来辅助完成这项工作。主要有两种技术路径：
                <ul>
                    <li>
                        <strong>基于提示工程的文本修正：</strong>通过向LLM提供带有示例的指令来引导其完成纠错任务。具体路径可分为以下两种：
                        <ul>
                            <li>
                                向LLM提供少量“错误-正确”范例，让其归纳模式并生成正则表达式或Python脚本，用于批量处理模式固定的错误。
                            </li>
                            <li>
                                将待修正文本连同样式范例一起发送给LLM，让其直接输出修正后的结果。
                            </li>
                        </ul>
                        该方法灵活性高、启动成本低，无需准备大量数据，但对于复杂和非模式化的错误，修正效果不稳定，处理大规模文本时效率和成本不是最优。
                    </li>
                    <li>
                        <strong>模型微调构建纠错模型：</strong>通过在一个预训练的LLM基础上，使用特定领域的“错误-正确”文本对其进行再训练，从而得到一个专注于OCR纠错任务的模型。该方法需要从所有书籍中抽样，构建一个统一且多样化的纠错数据集，用这份混合数据集微调出一个单一、通用的纠错模型。该方法准确率和稳定性高，擅长处理需要理解上下文的语义错误。但前期需要投入一定的人力成本来标注数据集，并需要相应的计算资源进行模型训练。
                    </li>
                </ul>
                最终应用应该是两种方法相结合，使用提示工程来处理简单的、模式化的错误，使用微调模型来处理复杂的、需要上下文理解的错误。这样可以在保证准确率的同时，降低整体成本和提高效率。
            </p>
            <h3 id="section2-3">2.3 知识抽取关键技术调研</h3>
            <p>
                知识抽取主要涉及分词、实体识别、关系抽取等技术。可以使用现有的开源工具和模型来完成这个任务。
                <ul>
                    <li>
                        <strong>分词：</strong>将连续的文本字符串切分成具有独立语义的词语或标记单元。这是所有下游NLP任务的基础。
                        <ul>
                            <li><strong>Jieba：</strong>经典的中文分词库，基于前缀词典和HMM模型，速度快，使用简单。</li>
                            <li><strong>HanLP、spaCy：</strong>更现代的NLP框架，集成了基于深度学习的分词模型，通常在处理歧义和未登录词方面效果更好。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>命名实体识别：</strong>从文本中识别并分类出具有特定意义的命名实体。spaCy和HanLP均提供了强大的预训练NER模型，并支持使用自有数据进行微调，以识别特定领域的实体。
                    </li>
                    <li>
                        <strong>关系抽取：</strong>在识别出实体后，进一步判断成对实体之间存在的语义关系。包括基于模板和词典的规则方法、传统监督学习方法，以及利用深度学习模型自动学习特征的方法。开源的OpenNRE等框架提供了多种关系抽取模型，在实践中需要针对特定关系类型标注数据并训练自定义模型。
                    </li>
                </ul>
            </p>

            <h2 id="section3">三、机器学习理论学习</h2>
            <h3 id="section3-1">3.1 假设空间</h3>
            <p>
                在机器学习中，学习过程可以理解为在所有可能的假设（hypothesis）所组成的空间中进行搜索，以找到与训练集数据相匹配的假设。这个集合被称为假设空间（Hypothesis Space）。
                <ul>
                    <li>学习的目标是找到一个在训练集上表现良好的假设，并能够对未见过的新样本进行合理的预测。</li>
                    <li>假设空间的大小通常取决于特征属性的取值情况。例如，若某个学习任务的三个属性分别有 n1、n2、n3 种可能的取值，则假设空间的大小为 (n1+1) × (n2+1) × (n3+1) + 1。</li>
                    <li>版本空间（Version Space）：指所有与训练集完全一致的假设集合。它是从假设空间中筛选出的一个子集。</li>
                    <li>在面对新的样本时，版本空间中的不同假设可能会给出不同的预测结果，这也是学习器在推广阶段需要处理的不确定性。</li>
                </ul>
            </p>

            <h3 id="section3-2">3.2 NFL定理推导</h3>
            <p>
                无免费午餐定理（No Free Lunch Theorem，NFL）表明：当对所有可能的目标函数进行均匀求和时，所有学习算法在总体上表现是相同的，即不存在一种算法在所有任务上都优于其他算法。
                <ul>
                    <li>设样本空间为 X，假设空间为 H。令 P(h | X, L<sub>a</sub>) 表示学习算法 L<sub>a</sub> 基于训练集 X 产生假设 h 的概率。</li>
                    <li>设 f 表示要学习的目标函数，算法 L<sub>a</sub> 在训练集之外的样本上的总误差可表示为：  
                        E<sub>out</sub>(L<sub>a</sub> | X, f) = Σ<sub>x ∈ X\X_train</sub> 1(h(x) ≠ f(x))。</li>
                    <li>在二分类问题中，目标函数 f 可以是从 X 到 {0,1} 的任意函数，即 f: X → {0,1}。函数空间大小为 {0,1}<sup>|X|</sup>。</li>
                    <li>若对所有可能的 f 按均匀分布取平均，则有：所有算法的期望误差相等，即  
                        Σ<sub>f</sub> E<sub>out</sub>(L<sub>a</sub> | X, f) = 常数（与 L<sub>a</sub> 无关）。</li>
                    <li>因此结论是：总误差与具体的学习算法无关，所有算法“同样好”。</li>
                </ul>
            </p>

            <h3 id="section3-3">3.3 PR指标的扩展</h3>
            <p>
                PR（Precision-Recall）指标主要用于评价分类器的性能，尤其是在类别分布不均衡时。  
                <ul>
                    <li>PR曲线（PR Curve）：通过对样本按照预测为正类的概率大小排序，逐个改变阈值，计算不同点的查准率（Precision）和查全率（Recall），并绘制曲线。</li>
                    <li>BEP（Break Even Point）：指查准率与查全率相等时的那个点，常被用来作为分类器性能的单一度量。</li>
                    <li>宏平均（Macro Average）：先分别计算每一类的查准率、查全率和 F1 值，再对这些值取平均。</li>
                    <li>微平均（Micro Average）：先将所有类别的混淆矩阵元素进行汇总，再基于汇总结果计算查准率、查全率和 F1 值。</li>
                    <li>相关公式如下：
                        <ul>
                            <li>Precision = TP / (TP + FP)</li>
                            <li>Recall = TP / (TP + FN)</li>
                            <li>F1 = 2 × (Precision × Recall) / (Precision + Recall)</li>
                        </ul>
                    </li>
                </ul>
            </p>

            <h3 id="section3-4">3.4 ROC与AUC</h3>
            <p>
                ROC（Receiver Operating Characteristic）曲线和 AUC（Area Under Curve）是分类器评价的重要指标。  
                <ul>
                    <li>ROC 曲线横轴为 FPR（False Positive Rate = FP / (FP+TN)），纵轴为 TPR（True Positive Rate = TP / (TP+FN)）。</li>
                    <li>AUC 指 ROC 曲线下的面积，取值范围在 [0,1] 之间，数值越大表示模型整体性能越好。</li>
                    <li>AUC 的数学表达式可写为：  
                        AUC = ∫<sub>0</sub><sup>1</sup> TPR(FPR) dFPR。</li>
                    <li>直观解释：AUC 等于从所有正样本与负样本对中，随机抽取一对 (x<sub>+</sub>, x<sub>-</sub>)，分类器将正样本得分排在负样本之前的概率。</li>
                </ul>
            </p>

            <h3 id="section3-5">3.5 非均等代价</h3>
            <p>
                在实际问题中，不同类型的错误可能会导致不同程度的损失，这时需要引入非均等代价（Unequal Cost）的概念。
                <ul>
                    <li>例如，在医疗诊断中，漏诊（将患病样本预测为健康）的代价通常远大于误诊。</li>
                    <li>代价敏感错误率（Cost-sensitive Error Rate）可表示为：  
                        Error = (1/N) Σ<sub>i=1</sub><sup>N</sup> C(y<sub>i</sub>, ŷ<sub>i</sub>)  
                        其中 C(y, ŷ) 表示真实类别 y 与预测类别 ŷ 下的错误代价。</li>
                    <li>这种代价函数允许我们根据实际场景对不同错误进行加权，得到更加符合应用需求的性能评价。</li>
                </ul>
            </p>

            <h3 id="section3-6">3.6 偏差-方差分解</h3>
            <p>
                偏差-方差分解（Bias-Variance Decomposition）是理解泛化误差的重要工具，尤其适用于回归任务。
                <ul>
                    <li>对于回归问题，学习器的泛化误差可以拆解为：  
                        E(f; D) = Bias²(x) + Var(x) + ε²，  
                        其中 Bias² 表示系统误差，Var 表示方差，ε² 表示不可约误差。</li>
                    <li>Bias 反映了模型在表达能力有限时产生的系统性偏差；Var 反映了模型对训练样本波动的敏感性。</li>
                    <li>在训练不足时，模型拟合能力较弱，偏差主导误差；随着训练加深，模型拟合能力增强，方差逐渐占主导；当训练充足时，学习器的拟合能力很强，方差成为主要误差来源。</li>
                    <li>因此，偏差与方差通常存在权衡关系（Bias-Variance Tradeoff），如何平衡两者是提升泛化性能的关键。</li>
                </ul>
            </p>


            <h2 id="section4">四、知识图谱理论学习</h2>
            <h3 id="section4-1">4.1 知识图谱问答</h3>
            <p>
                知识图谱问答是智能问答系统中的核心研究方向之一。当前常见的智能问答形式包括文本问答、社区问答、表格问答和视觉问答等，而知识图谱问答在这些形式之间往往起到桥梁作用，能够为不同类型的问答提供结构化的知识支撑。其目标不仅是提高机器回答问题的准确性，还在于推动人与机器之间更自然的交互。这涉及两个基本问题：如何让机器更好地理解自然语言，以及如何让机器有效地获取和表示知识。尽管目前相关研究已经取得了显著进展，但这两个问题仍面临诸多挑战。
                <ul>
                    从技术路线来看，知识图谱问答大体可以分为以下几类方法：
                    <li>
                        <strong>基于查询模板的方法：</strong>  
                        通过预定义的查询模板，将自然语言问题转化为可以在知识图谱上执行的查询语句。优点是响应速度快、准确率较高，并且能够回答一定程度上的复杂问题；缺点是人工设计的模板往往无法覆盖用户问题的多种表达方式，且需要建立庞大的模板库，维护成本高，查询效率也会受到影响。
                    </li>
                    <li>
                        <strong>基于语义解析的方法：</strong>  
                        与模板方法类似，语义解析的目标也是得到一个可直接在知识图谱中执行的逻辑表达式，但它强调直接从问句中解析逻辑形式。通常包括短语检测、资源映射、语义组合和逻辑表达式生成四个步骤。缺点是过于依赖对问句的解析，而对知识图谱内部资源的利用不足。
                    </li>
                    <li>
                        <strong>基于检索排序的方法：</strong>  
                        将用户问题转化为候选答案的检索与排序任务。优势在于框架灵活、可融合多种特征，并且容易与其他方法结合；缺点是依赖特征工程，容易受到错误传递影响，不擅长语义组合，更难以处理推理问题。
                    </li>
                    <li>
                        <strong>基于深度学习的方法：</strong>  
                        借助深度神经网络，模型能够深入表征问句，同时挖掘知识图谱中实体与关系的语义表示，从而提升问句理解与候选答案排序效果。缺点是深度模型需要大量训练语料，在复杂逻辑问题上效果未必理想；同时可解释性较差，在推理类任务中仍显不足。
                    </li>
                </ul>
                总的来看，语言理解与知识表示是问答系统的核心要素，而知识图谱在其中发挥着关键作用。四类方法各有优缺点：模板方法高效可靠，语义解析方法强调逻辑表达，检索排序方法注重候选答案筛选，深度学习方法则推动技术发展趋势。未来的发展需要在复杂问句处理、逻辑推理和可解释性上进一步提升。
            </p>

            <h2 id="section5">五、文献阅读</h2>
            <h3 id="section5-1">5.1 Self-supervised Quantized Representation for Seamlessly Integrating Knowledge Graphs with Large Language Models</h3>
            <p>
                这篇论文聚焦于如何让知识图谱（KG）与大语言模型（LLM）真正“说上话”。作者提出的 SSQR（Self-supervised Quantized Representation，自监督量化表示）把每个实体学习成一串离散 tokens（既含结构也含语义），再把这些 tokens 当作新词直接并入 LLM 的词表，从而无需改动模型结构就能把 KG 知识无缝注入到 LLM。核心直觉是：把复杂的图知识“翻译”为 LLM 最擅长处理的离散 token 序列。
                <ul>
                    <strong>为什么需要这样做？</strong>
                    <li>LLM 易出现“知识幻觉”，而 KG 提供可校验的结构化事实。</li>
                    <li>把三元组直接转成提示会导致 token 爆炸（多跳邻居更严重）。</li>
                    <li>采样提示易丢结构；连续向量对齐需额外适配器且效果不稳；既有无监督量化方法难以同时刻画结构与语义。</li>
                </ul>
                <ul>
                    <strong>方法概览（两阶段）：</strong>
                    <li><em>阶段一：自监督量化表示学习</em>——用 GCN 建模图结构；用强文本嵌入（如 text-embedding-3-large）做语义蒸馏；通过可学习码本做矢量量化，把连续表示变成固定长度的 code 索引序列；训练目标同时涵盖三元组有效性（结构）与语义对齐（语义）。</li>
                    <li><em>阶段二：基于 SSQR 微调 LLM</em>——把 KG 任务改写为指令式问答，在输入中直接嵌入实体的 SSQR 编码；LLM 像处理普通 tokens 一样学习并利用这些“新词”。</li>
                </ul>
                <ul>
                    <strong>实验结论（以 WN18RR、FB15k-237 等为代表）：</strong>
                    <li>SSQR 生成的表示显著优于 NodePiece、EARL 等无监督量化方法。</li>
                    <li>效率出色：每个实体约 16 个 token，极大节省计算与上下文预算。</li>
                    <li>性能领先：用 SSQR 微调的 LLaMA 系列在链接预测、三元组分类等任务上优于传统 KG 嵌入与多数基于 LLM 的方案。</li>
                    <li>可分性强：t-SNE 显示 SSQR tokens 在嵌入空间与普通词明显分离，消融也表明没有 SSQR 的微调更易过拟合。</li>
                </ul>
                SSQR 用一种工程上极其顺手的方式把 KG 变成 LLM 能直接摄取的“语言”，既缓解幻觉，又避免长上下文负担，是走向高效、可靠、知识密集型应用的一条很有前景的路径。
            </p>
        </div>
    </div>
</body>
</html>