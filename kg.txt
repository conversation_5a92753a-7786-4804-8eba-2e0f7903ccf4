图算法与图数据分析
1. 图的基本知识
 图能够建模很多客观世界的复杂问题，图技术在研究疾病交互网络、交通网络、社交网络等很多领域有重要应用
 常见的图和网络模型有无尺度网络和随机网络等，它们分别被用来建模不同类型的复杂问题
 知识图谱可以看作是图的一种应用，但与普通的图还是有非常大的区别。图的一些理论和算法多可以用来处理和分析知识图谱数据

2. 基础图算法
 图的基本算法包括路径发现与图搜索、图的中心度等特性分析、社区发现与分析等
 这些传统的图算法对于途中个体和宏观两个视角的挖掘与分析都比较重要
 这些算法仍然以符号匹配为主，随着深度学习的兴起，基于表示学习的图挖掘与分析逐步兴起

3. 图神经网络与图表示学习
 DeepWalk
 node2vec
 LINE
 NetMF
 Metapath2vec
 GCN
 VAGE
 GAT
 GraphSAGE
 GTN
 层次化模型
 GPT-GNN
 GCC

4. 图神经网络与知识图谱
 图结构是一种强有力的数据建模方法，知识图谱是利用图结构建模知识的方法
 基于图论的系列图算法可以有效的用来对知识图谱进行挖掘、分析和可视化
 图嵌入和图神经网络都是重要的对图的结构特征进行处理的表示学习的方法，与知识图谱嵌入和规则学习等方法不同，图表示学习方法侧重于图结构的处理
 知识图谱嵌入模型和规则学习等方法更加侧重于语义和逻辑结构特征的学习，而非图结构的学习
 更好的知识图谱表示学习方法需要综合利用好语义、逻辑结构的特征学习和图结构的特征学习等多种方法

知识图谱技术发展
1. 多模态知识图谱
 知识图谱是链接数据的概念，本来就应该是多模态的，现有很多商用知识图谱都已经通过图谱将各种模态的数据进行关联，并提高多模态的语义搜索能力
 多模态知识图谱可以发挥不同模态数据中所包含知识的互补性，相互增强，相互增强，相互补充。一方面，我们可以利用多模态数据来进一步补全知识图谱，另外一方面，知识图谱也可以提升多模态任务的效率
 多模态知识图谱有很多值得深入研究的方向，例如：多模态关系预测与推理、多模态知识问答、多模态实体对齐与实体链接、多模态推荐计算等
 目前有关多模态知识图谱的研究，不管是经验方法还是图谱数据的构建，都还有很大的发展和创新空间

2. 知识图谱与语言预训练
 知识对于语言的理解至关重要，在语言预训练模型大行其道的当下，将知识融入到语言预训练模型中是重要的技术发展方向
 将知识图谱融入到语言预训练模型中大致有三种方法，包括直接把知识图谱表示向量作为特征输入的ERNIE和KnowBERT等模型；通过设计新的预训练任务实现知识植入的KEPLER和WKLM等模型；通过增加额外的模块的K-ADAPTER等模型
 知识的植入未必总是有效的，实验表明知识植入对于那些低资源任务和低频的实体是有价值的，但因为外部知识的引入也可能带来噪音，因此也可能反而对语言模型带来模型损失

3. 事理知识图谱
 事理图谱是一类以事理和事件为中心的知识图谱，侧重于刻画事件和事理之间因果、顺承、条件、互反等逻辑关系
 事理是知识图谱动起来的神经，知识图谱是事理运行的血肉载体，描述事理逻辑架构的事理知识图谱与刻画实体逻辑关系知识图谱可以相互补充，更好解决现实的问题

4. 知识图谱与低资源学习
 知识（图谱）的获取和构建，特别是常识类的知识获取绝大部分都是长尾问题，自动化知识获取的主要难点是在长尾知识，因此，低资源学习是知识图谱构建的不可获取的技术手段
 知识图谱可用来建模语义空间，从而建立类别之间的关联关系，从而更好的帮助解决零样本预测等低资源问题
 只是可以看作是质量更好、表示更规范的数据，或者是融入更多人的先验的数据。解决低资源问题应该充分发挥知识的作用
