medllm-finetune-rag部署过程：

首先在Windows上尝试部署，直接用requirements.txt安装各种依赖，遇到依赖冲突和解析错误。随后尝试用shell脚本配置，出现系统默认用文本编辑器打开文件而不是执行脚本，改为在WSL上部署。

在WSL上部署，忘记核查python版本，直接在全局的python环境中安装依赖，版本过低，部署不成功；之后更换为python 3.10后再次运行shell脚本，成功配置好环境。环境配置好后，又发现个人电脑的消费级GPU不支持模型运行，还是需要部署到服务器上。 

在服务器上部署时出现了新的问题：原来在自己的本地执行环境配置的脚本时曾遇到网络问题，所以使用了clash tun模式，成功执行了环境配置脚本。但是由于访问服务器需要通过深信服的办公助手，这个办公助手会拦截它认为不安全的流量，所以tun失效了。之后换了github的代理也不OK。最后的解决方案是：先使用ssh协议从github上clone了出问题的unsloth仓库，然后在shell脚本中修改为从本地路径安装。 

note:这还是第一次知道pip原来也可以从本地安装。相关命令：pip install -e "$HOME/unsloth[colab-new]" 。添加-e（Editable）参数的原因是，使用这种方式，不会将包的源代码复制到sitepackages目录，而是创建一个指向本地项目文件夹的符号链接，这样的话，当在本地的unsloth文件夹中通过git pull拉取了更新后，这些更新会立即在python环境中生效而不需要重新安装。符号链接新用法get。

部署好后，发现仓库代码更新了。为了在保留本地修改的同时安全地更新代码，使用git stash命令先将所有未提交的修改暂时存起来，让工作目录恢复到干净状态；然后使用git pull命令从远程仓库拉取最新代码；再使用git stash pop命令将暂存的修改应用到更新后的代码上。git新命令get。

在执行测试运行的时候，发现项目的目录结构和readme文件中给的测试运行指导不匹配，接下来研究该怎么运行、使用这个项目。